import 'package:jojo_flutter_base/base.dart';
import 'package:jojo_flutter_base/base_namespace.dart';
import 'package:jojo_flutter_base/config/address.dart';
import 'package:jojo_flutter_plan_pkg/pages/team_study_activity_page/models/team_study_activity_model.dart';
import 'package:retrofit/http.dart';
import '../common/dio/use.dart';
part 'team_study_activity_api.g.dart';

@RestApi()
abstract class TeamStudyActivityApi {
  factory TeamStudyActivityApi(Dio dio, {String baseUrl}) =
      _TeamStudyActivityApi;

  // 获取多人学活动信息 0: 固定参数，代表用户自己  subjectType: 不传默认是语文
  @GET("/api/pagani/study-teams/0")
  Future<TeamStudyActivityModel> getTeamStudyActivityInfo(
    @Query('subjectType') int? subjectType,
    @Query('scene') String scene,
  );

  /// 能量收集/奖励领取
  @POST("/api/pagani/study-teams/{teamId}/rewards")
  Future<void> getMapRewards(
    @Path('teamId') int teamId, // 小队id
    @Path('type') int type, // 类型(1能量2奖励)
    @Query('memberIds') int? memberIds, // 成员id(type=1)
    @Query('nodeId') int? nodeId, // 节点id(type=2, 引导奖励传0)
  );

  /// 获取队伍邀请列表
  @GET("/api/pagani/study-teams/{teamId}/invitations")
  Future<TeamInvitationListResponse> getTeamInvitations(
    @Path('teamId') int teamId, // 小队id
  );

  /// 邀请用户加入队伍
  @POST("/api/pagani/study-teams/{teamId}/invitations")
  Future<void> inviteUsersToTeam(
    @Path('teamId') int teamId, // 小队id
    @Body() Map<String, dynamic> body, // 请求体
  );


}

final teamStudyApi =
    TeamStudyActivityApi(pageDio, baseUrl: BaseAddress.baseTinmanApiPath);

final mockTeamStudyApi = MockTeamStudyActivityApi();

class MockTeamStudyActivityApi implements TeamStudyActivityApi {
  @override
  Future<TeamStudyActivityModel> getTeamStudyActivityInfo(
      int? subjectType, String scene) async {
    
    final activityMap = {
      "teamId": 101,
      "status": 1, //活动状态(0未解锁,1组队中,2进行中,3已结束)
      "startTime": 1724630400, // 2025-08-26 00:00:00 UTC
      "endTime": 1725062400, // 2025-08-31 00:00:00 UTC
      "subjectType": 1,
      "subjectName": "阅读",
      "resource": "math_banner.jpg",
      "mapResource": "campus_map.png",
      "current": 1,
      "total": 30,
      "isUnReceiveReward": 0,
      "nodeRewardType": 2,

      "joinActivityRewardList": [
        {
          "id": 201,
          "type": 1,
          "num": 1,
          "name": "积分奖励",
          "desc": "完成章节获得",
          "img": "reward_icon.png",
          "isReceive": 0,
        }
      ],

      "teamMemberList": [
        {
          "memberId": 1001,
          "photo": "https://jojopublicuat.tinman.cn/cc/cc-admin/course/179646760951535616/47d2d9262787310b61a99cb04a84c77b1599471124941.png",
          "nickname": "小明",
          "dayCount": 5,
          "isSelf": 1,
          "pendingCollectionEnergy": 2,
          "dressList": [
            {"resource": "costume_red.png"},
            {"resource": "glasses_black.png"}
          ]
        },
        {
          "memberId": 1002,
          "photo": "user_1002.jpg",
          "nickname": "小红",
          "dayCount": 3,
          "isSelf": 0,
          "pendingCollectionEnergy": 4,
          "dressList": [
            {"resource": "hat_blue.png"}
          ]
        },
        // {
        //   "memberId": 1003,
        //   "photo": "user_1002.jpg",
        //   "nickname": "小张",
        //   "dayCount": 4,
        //   "isSelf": 0,
        //   "pendingCollectionEnergy": 8,
        //   "dressList": [
        //     {"resource": "hat_blue.png"}
        //   ]
        // }
      ],

      "mapNodeList": [
        {
          "teamId": 101,
          "nodeId": 301,
          "number": 5,
          "isFinish": 1,
          "isReceiveReward": 1,
          "receiveTime": 1724716800,
          "nodeRewardType": 1,
          "rewardList": [
            {
              "id": 301,
              "type": 3,
              "num": 100,
              "name": "经验值",
              "img": "exp_icon.png",
              "isReceive": 1
            }
          ]
        },
        {
          "nodeId": 302,
          "number": 3,
          "isFinish": 0,
          "isReceiveReward": 0,
          "nodeRewardType": 2
        }
      ]
    };
    // 返回默认数据或根据参数返回不同数据
    final activity = TeamStudyActivityModel.fromJson(activityMap);
    return activity;
  }

  @override
  Future<void> getMapRewards(
      int teamId, int type, int? memberIds, int? nodeId) async {
    // 模拟网络延迟
    await Future.delayed(const Duration(milliseconds: 300));

    // 模拟奖励领取逻辑
    print(
        '领取奖励: teamId=$teamId, type=$type, memberId=$memberIds, nodeId=$nodeId');

    // 这里可以添加奖励领取的业务逻辑
    // 例如更新本地状态等
  }

  @override
  Future<TeamInvitationListResponse> getTeamInvitations(int teamId) async {
    // 模拟网络延迟
    await Future.delayed(const Duration(milliseconds: 500));

    print('Mock API: 获取队伍邀请列表, teamId=$teamId');

    // 模拟邀请列表数据 - 包含各种状态的用户
    final invitationData = {
      "inviteList": [
        {
          "userId": 1001,
          "nickname": "泡泡",
          "dressImg": "https://jojopublicuat.tinman.cn/cc/cc-admin/course/179646760951535616/47d2d9262787310b61a99cb04a84c77b1599471124941.png",
          "learnDay": 235,
          "dayCount": 1,
          "bestDayCount": 20,
          "isInvite": 1, // 已邀请
          "isPatner": 1, // 是学伴
        },
        {
          "userId": 1002,
          "nickname": "小明",
          "dressImg": "https://jojopublicuat.tinman.cn/cc/cc-admin/course/179646760951535616/47d2d9262787310b61a99cb04a84c77b1599471124941.png",
          "learnDay": 180,
          "dayCount": 5,
          "bestDayCount": 15,
          "isInvite": 0, // 未邀请
          "isPatner": 0, // 不是学伴
        },
        {
          "userId": 1003,
          "nickname": "小红",
          "dressImg": "",
          "learnDay": 320,
          "dayCount": 3,
          "bestDayCount": 25,
          "isInvite": 1, // 已邀请
          "isPatner": 0, // 不是学伴
        },
        {
          "userId": 1004,
          "nickname": "学霸小王",
          "dressImg": "https://jojopublicuat.tinman.cn/cc/cc-admin/course/179646760951535616/47d2d9262787310b61a99cb04a84c77b1599471124941.png",
          "learnDay": 500,
          "dayCount": 15,
          "bestDayCount": 50,
          "isInvite": 0, // 未邀请
          "isPatner": 1, // 是学伴
        },
        {
          "userId": 1005,
          "nickname": "努力的小李",
          "dressImg": "",
          "learnDay": 88,
          "dayCount": 2,
          "bestDayCount": 8,
          "isInvite": 0, // 未邀请
          "isPatner": 0, // 不是学伴
        },
      ]
    };

    return TeamInvitationListResponse.fromJson(invitationData);
  }

  @override
  Future<void> inviteUsersToTeam(int teamId, Map<String, dynamic> body) async {
    // 模拟网络延迟
    await Future.delayed(const Duration(milliseconds: 800));

    print('Mock API: 邀请用户加入队伍');
    print('teamId: $teamId');
    print('请求参数: $body');

    final inviteUserIds = body['inviteUserIds'] as List<int>?;
    if (inviteUserIds != null && inviteUserIds.isNotEmpty) {
      print('邀请的用户ID列表: $inviteUserIds');
      // 模拟邀请成功
      print('邀请发送成功！');
    } else {
      throw Exception('邀请用户列表不能为空');
    }
  }
}
