// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'team_study_activity_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#custom-getters-and-methods');

TeamStudyActivityModel _$TeamStudyActivityModelFromJson(
    Map<String, dynamic> json) {
  return _TeamStudyActivityModel.fromJson(json);
}

/// @nodoc
mixin _$TeamStudyActivityModel {
  int? get teamId => throw _privateConstructorUsedError;
  int? get status =>
      throw _privateConstructorUsedError; //活动状态(0未解锁,1组队中,2进行中,3已结束)
  int? get startTime => throw _privateConstructorUsedError;
  int? get endTime => throw _privateConstructorUsedError;
  int? get subjectType => throw _privateConstructorUsedError;
  String? get subjectName => throw _privateConstructorUsedError;
  String? get resource => throw _privateConstructorUsedError;
  String? get mapResource => throw _privateConstructorUsedError;
  int? get current => throw _privateConstructorUsedError;
  int? get total => throw _privateConstructorUsedError;
  int? get isUnReceiveReward => throw _privateConstructorUsedError;
  int? get nodeRewardType => throw _privateConstructorUsedError;
  List<TeamStudyActivityReward>? get joinActivityRewardList =>
      throw _privateConstructorUsedError;
  List<TeamStudyActivityMember>? get teamMemberList =>
      throw _privateConstructorUsedError;
  List<TeamStudySeaMapPointState>? get mapNodeList =>
      throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $TeamStudyActivityModelCopyWith<TeamStudyActivityModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $TeamStudyActivityModelCopyWith<$Res> {
  factory $TeamStudyActivityModelCopyWith(TeamStudyActivityModel value,
          $Res Function(TeamStudyActivityModel) then) =
      _$TeamStudyActivityModelCopyWithImpl<$Res, TeamStudyActivityModel>;
  @useResult
  $Res call(
      {int? teamId,
      int? status,
      int? startTime,
      int? endTime,
      int? subjectType,
      String? subjectName,
      String? resource,
      String? mapResource,
      int? current,
      int? total,
      int? isUnReceiveReward,
      int? nodeRewardType,
      List<TeamStudyActivityReward>? joinActivityRewardList,
      List<TeamStudyActivityMember>? teamMemberList,
      List<TeamStudySeaMapPointState>? mapNodeList});
}

/// @nodoc
class _$TeamStudyActivityModelCopyWithImpl<$Res,
        $Val extends TeamStudyActivityModel>
    implements $TeamStudyActivityModelCopyWith<$Res> {
  _$TeamStudyActivityModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? teamId = freezed,
    Object? status = freezed,
    Object? startTime = freezed,
    Object? endTime = freezed,
    Object? subjectType = freezed,
    Object? subjectName = freezed,
    Object? resource = freezed,
    Object? mapResource = freezed,
    Object? current = freezed,
    Object? total = freezed,
    Object? isUnReceiveReward = freezed,
    Object? nodeRewardType = freezed,
    Object? joinActivityRewardList = freezed,
    Object? teamMemberList = freezed,
    Object? mapNodeList = freezed,
  }) {
    return _then(_value.copyWith(
      teamId: freezed == teamId
          ? _value.teamId
          : teamId // ignore: cast_nullable_to_non_nullable
              as int?,
      status: freezed == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as int?,
      startTime: freezed == startTime
          ? _value.startTime
          : startTime // ignore: cast_nullable_to_non_nullable
              as int?,
      endTime: freezed == endTime
          ? _value.endTime
          : endTime // ignore: cast_nullable_to_non_nullable
              as int?,
      subjectType: freezed == subjectType
          ? _value.subjectType
          : subjectType // ignore: cast_nullable_to_non_nullable
              as int?,
      subjectName: freezed == subjectName
          ? _value.subjectName
          : subjectName // ignore: cast_nullable_to_non_nullable
              as String?,
      resource: freezed == resource
          ? _value.resource
          : resource // ignore: cast_nullable_to_non_nullable
              as String?,
      mapResource: freezed == mapResource
          ? _value.mapResource
          : mapResource // ignore: cast_nullable_to_non_nullable
              as String?,
      current: freezed == current
          ? _value.current
          : current // ignore: cast_nullable_to_non_nullable
              as int?,
      total: freezed == total
          ? _value.total
          : total // ignore: cast_nullable_to_non_nullable
              as int?,
      isUnReceiveReward: freezed == isUnReceiveReward
          ? _value.isUnReceiveReward
          : isUnReceiveReward // ignore: cast_nullable_to_non_nullable
              as int?,
      nodeRewardType: freezed == nodeRewardType
          ? _value.nodeRewardType
          : nodeRewardType // ignore: cast_nullable_to_non_nullable
              as int?,
      joinActivityRewardList: freezed == joinActivityRewardList
          ? _value.joinActivityRewardList
          : joinActivityRewardList // ignore: cast_nullable_to_non_nullable
              as List<TeamStudyActivityReward>?,
      teamMemberList: freezed == teamMemberList
          ? _value.teamMemberList
          : teamMemberList // ignore: cast_nullable_to_non_nullable
              as List<TeamStudyActivityMember>?,
      mapNodeList: freezed == mapNodeList
          ? _value.mapNodeList
          : mapNodeList // ignore: cast_nullable_to_non_nullable
              as List<TeamStudySeaMapPointState>?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_TeamStudyActivityModelCopyWith<$Res>
    implements $TeamStudyActivityModelCopyWith<$Res> {
  factory _$$_TeamStudyActivityModelCopyWith(_$_TeamStudyActivityModel value,
          $Res Function(_$_TeamStudyActivityModel) then) =
      __$$_TeamStudyActivityModelCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {int? teamId,
      int? status,
      int? startTime,
      int? endTime,
      int? subjectType,
      String? subjectName,
      String? resource,
      String? mapResource,
      int? current,
      int? total,
      int? isUnReceiveReward,
      int? nodeRewardType,
      List<TeamStudyActivityReward>? joinActivityRewardList,
      List<TeamStudyActivityMember>? teamMemberList,
      List<TeamStudySeaMapPointState>? mapNodeList});
}

/// @nodoc
class __$$_TeamStudyActivityModelCopyWithImpl<$Res>
    extends _$TeamStudyActivityModelCopyWithImpl<$Res,
        _$_TeamStudyActivityModel>
    implements _$$_TeamStudyActivityModelCopyWith<$Res> {
  __$$_TeamStudyActivityModelCopyWithImpl(_$_TeamStudyActivityModel _value,
      $Res Function(_$_TeamStudyActivityModel) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? teamId = freezed,
    Object? status = freezed,
    Object? startTime = freezed,
    Object? endTime = freezed,
    Object? subjectType = freezed,
    Object? subjectName = freezed,
    Object? resource = freezed,
    Object? mapResource = freezed,
    Object? current = freezed,
    Object? total = freezed,
    Object? isUnReceiveReward = freezed,
    Object? nodeRewardType = freezed,
    Object? joinActivityRewardList = freezed,
    Object? teamMemberList = freezed,
    Object? mapNodeList = freezed,
  }) {
    return _then(_$_TeamStudyActivityModel(
      teamId: freezed == teamId
          ? _value.teamId
          : teamId // ignore: cast_nullable_to_non_nullable
              as int?,
      status: freezed == status
          ? _value.status
          : status // ignore: cast_nullable_to_non_nullable
              as int?,
      startTime: freezed == startTime
          ? _value.startTime
          : startTime // ignore: cast_nullable_to_non_nullable
              as int?,
      endTime: freezed == endTime
          ? _value.endTime
          : endTime // ignore: cast_nullable_to_non_nullable
              as int?,
      subjectType: freezed == subjectType
          ? _value.subjectType
          : subjectType // ignore: cast_nullable_to_non_nullable
              as int?,
      subjectName: freezed == subjectName
          ? _value.subjectName
          : subjectName // ignore: cast_nullable_to_non_nullable
              as String?,
      resource: freezed == resource
          ? _value.resource
          : resource // ignore: cast_nullable_to_non_nullable
              as String?,
      mapResource: freezed == mapResource
          ? _value.mapResource
          : mapResource // ignore: cast_nullable_to_non_nullable
              as String?,
      current: freezed == current
          ? _value.current
          : current // ignore: cast_nullable_to_non_nullable
              as int?,
      total: freezed == total
          ? _value.total
          : total // ignore: cast_nullable_to_non_nullable
              as int?,
      isUnReceiveReward: freezed == isUnReceiveReward
          ? _value.isUnReceiveReward
          : isUnReceiveReward // ignore: cast_nullable_to_non_nullable
              as int?,
      nodeRewardType: freezed == nodeRewardType
          ? _value.nodeRewardType
          : nodeRewardType // ignore: cast_nullable_to_non_nullable
              as int?,
      joinActivityRewardList: freezed == joinActivityRewardList
          ? _value._joinActivityRewardList
          : joinActivityRewardList // ignore: cast_nullable_to_non_nullable
              as List<TeamStudyActivityReward>?,
      teamMemberList: freezed == teamMemberList
          ? _value._teamMemberList
          : teamMemberList // ignore: cast_nullable_to_non_nullable
              as List<TeamStudyActivityMember>?,
      mapNodeList: freezed == mapNodeList
          ? _value._mapNodeList
          : mapNodeList // ignore: cast_nullable_to_non_nullable
              as List<TeamStudySeaMapPointState>?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_TeamStudyActivityModel implements _TeamStudyActivityModel {
  _$_TeamStudyActivityModel(
      {this.teamId,
      this.status,
      this.startTime,
      this.endTime,
      this.subjectType,
      this.subjectName,
      this.resource,
      this.mapResource,
      this.current,
      this.total,
      this.isUnReceiveReward,
      this.nodeRewardType,
      final List<TeamStudyActivityReward>? joinActivityRewardList,
      final List<TeamStudyActivityMember>? teamMemberList,
      final List<TeamStudySeaMapPointState>? mapNodeList})
      : _joinActivityRewardList = joinActivityRewardList,
        _teamMemberList = teamMemberList,
        _mapNodeList = mapNodeList;

  factory _$_TeamStudyActivityModel.fromJson(Map<String, dynamic> json) =>
      _$$_TeamStudyActivityModelFromJson(json);

  @override
  final int? teamId;
  @override
  final int? status;
//活动状态(0未解锁,1组队中,2进行中,3已结束)
  @override
  final int? startTime;
  @override
  final int? endTime;
  @override
  final int? subjectType;
  @override
  final String? subjectName;
  @override
  final String? resource;
  @override
  final String? mapResource;
  @override
  final int? current;
  @override
  final int? total;
  @override
  final int? isUnReceiveReward;
  @override
  final int? nodeRewardType;
  final List<TeamStudyActivityReward>? _joinActivityRewardList;
  @override
  List<TeamStudyActivityReward>? get joinActivityRewardList {
    final value = _joinActivityRewardList;
    if (value == null) return null;
    if (_joinActivityRewardList is EqualUnmodifiableListView)
      return _joinActivityRewardList;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  final List<TeamStudyActivityMember>? _teamMemberList;
  @override
  List<TeamStudyActivityMember>? get teamMemberList {
    final value = _teamMemberList;
    if (value == null) return null;
    if (_teamMemberList is EqualUnmodifiableListView) return _teamMemberList;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  final List<TeamStudySeaMapPointState>? _mapNodeList;
  @override
  List<TeamStudySeaMapPointState>? get mapNodeList {
    final value = _mapNodeList;
    if (value == null) return null;
    if (_mapNodeList is EqualUnmodifiableListView) return _mapNodeList;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  String toString() {
    return 'TeamStudyActivityModel(teamId: $teamId, status: $status, startTime: $startTime, endTime: $endTime, subjectType: $subjectType, subjectName: $subjectName, resource: $resource, mapResource: $mapResource, current: $current, total: $total, isUnReceiveReward: $isUnReceiveReward, nodeRewardType: $nodeRewardType, joinActivityRewardList: $joinActivityRewardList, teamMemberList: $teamMemberList, mapNodeList: $mapNodeList)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_TeamStudyActivityModel &&
            (identical(other.teamId, teamId) || other.teamId == teamId) &&
            (identical(other.status, status) || other.status == status) &&
            (identical(other.startTime, startTime) ||
                other.startTime == startTime) &&
            (identical(other.endTime, endTime) || other.endTime == endTime) &&
            (identical(other.subjectType, subjectType) ||
                other.subjectType == subjectType) &&
            (identical(other.subjectName, subjectName) ||
                other.subjectName == subjectName) &&
            (identical(other.resource, resource) ||
                other.resource == resource) &&
            (identical(other.mapResource, mapResource) ||
                other.mapResource == mapResource) &&
            (identical(other.current, current) || other.current == current) &&
            (identical(other.total, total) || other.total == total) &&
            (identical(other.isUnReceiveReward, isUnReceiveReward) ||
                other.isUnReceiveReward == isUnReceiveReward) &&
            (identical(other.nodeRewardType, nodeRewardType) ||
                other.nodeRewardType == nodeRewardType) &&
            const DeepCollectionEquality().equals(
                other._joinActivityRewardList, _joinActivityRewardList) &&
            const DeepCollectionEquality()
                .equals(other._teamMemberList, _teamMemberList) &&
            const DeepCollectionEquality()
                .equals(other._mapNodeList, _mapNodeList));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      teamId,
      status,
      startTime,
      endTime,
      subjectType,
      subjectName,
      resource,
      mapResource,
      current,
      total,
      isUnReceiveReward,
      nodeRewardType,
      const DeepCollectionEquality().hash(_joinActivityRewardList),
      const DeepCollectionEquality().hash(_teamMemberList),
      const DeepCollectionEquality().hash(_mapNodeList));

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_TeamStudyActivityModelCopyWith<_$_TeamStudyActivityModel> get copyWith =>
      __$$_TeamStudyActivityModelCopyWithImpl<_$_TeamStudyActivityModel>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_TeamStudyActivityModelToJson(
      this,
    );
  }
}

abstract class _TeamStudyActivityModel implements TeamStudyActivityModel {
  factory _TeamStudyActivityModel(
          {final int? teamId,
          final int? status,
          final int? startTime,
          final int? endTime,
          final int? subjectType,
          final String? subjectName,
          final String? resource,
          final String? mapResource,
          final int? current,
          final int? total,
          final int? isUnReceiveReward,
          final int? nodeRewardType,
          final List<TeamStudyActivityReward>? joinActivityRewardList,
          final List<TeamStudyActivityMember>? teamMemberList,
          final List<TeamStudySeaMapPointState>? mapNodeList}) =
      _$_TeamStudyActivityModel;

  factory _TeamStudyActivityModel.fromJson(Map<String, dynamic> json) =
      _$_TeamStudyActivityModel.fromJson;

  @override
  int? get teamId;
  @override
  int? get status;
  @override //活动状态(0未解锁,1组队中,2进行中,3已结束)
  int? get startTime;
  @override
  int? get endTime;
  @override
  int? get subjectType;
  @override
  String? get subjectName;
  @override
  String? get resource;
  @override
  String? get mapResource;
  @override
  int? get current;
  @override
  int? get total;
  @override
  int? get isUnReceiveReward;
  @override
  int? get nodeRewardType;
  @override
  List<TeamStudyActivityReward>? get joinActivityRewardList;
  @override
  List<TeamStudyActivityMember>? get teamMemberList;
  @override
  List<TeamStudySeaMapPointState>? get mapNodeList;
  @override
  @JsonKey(ignore: true)
  _$$_TeamStudyActivityModelCopyWith<_$_TeamStudyActivityModel> get copyWith =>
      throw _privateConstructorUsedError;
}

TeamStudyActivityReward _$TeamStudyActivityRewardFromJson(
    Map<String, dynamic> json) {
  return _TeamStudyActivityReward.fromJson(json);
}

/// @nodoc
mixin _$TeamStudyActivityReward {
  int? get id => throw _privateConstructorUsedError;
  int? get type => throw _privateConstructorUsedError;
  int? get num => throw _privateConstructorUsedError;
  String? get name => throw _privateConstructorUsedError;
  String? get desc => throw _privateConstructorUsedError;
  String? get img => throw _privateConstructorUsedError;
  int? get isReceive => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $TeamStudyActivityRewardCopyWith<TeamStudyActivityReward> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $TeamStudyActivityRewardCopyWith<$Res> {
  factory $TeamStudyActivityRewardCopyWith(TeamStudyActivityReward value,
          $Res Function(TeamStudyActivityReward) then) =
      _$TeamStudyActivityRewardCopyWithImpl<$Res, TeamStudyActivityReward>;
  @useResult
  $Res call(
      {int? id,
      int? type,
      int? num,
      String? name,
      String? desc,
      String? img,
      int? isReceive});
}

/// @nodoc
class _$TeamStudyActivityRewardCopyWithImpl<$Res,
        $Val extends TeamStudyActivityReward>
    implements $TeamStudyActivityRewardCopyWith<$Res> {
  _$TeamStudyActivityRewardCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = freezed,
    Object? type = freezed,
    Object? num = freezed,
    Object? name = freezed,
    Object? desc = freezed,
    Object? img = freezed,
    Object? isReceive = freezed,
  }) {
    return _then(_value.copyWith(
      id: freezed == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as int?,
      type: freezed == type
          ? _value.type
          : type // ignore: cast_nullable_to_non_nullable
              as int?,
      num: freezed == num
          ? _value.num
          : num // ignore: cast_nullable_to_non_nullable
              as int?,
      name: freezed == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String?,
      desc: freezed == desc
          ? _value.desc
          : desc // ignore: cast_nullable_to_non_nullable
              as String?,
      img: freezed == img
          ? _value.img
          : img // ignore: cast_nullable_to_non_nullable
              as String?,
      isReceive: freezed == isReceive
          ? _value.isReceive
          : isReceive // ignore: cast_nullable_to_non_nullable
              as int?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_TeamStudyActivityRewardCopyWith<$Res>
    implements $TeamStudyActivityRewardCopyWith<$Res> {
  factory _$$_TeamStudyActivityRewardCopyWith(_$_TeamStudyActivityReward value,
          $Res Function(_$_TeamStudyActivityReward) then) =
      __$$_TeamStudyActivityRewardCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {int? id,
      int? type,
      int? num,
      String? name,
      String? desc,
      String? img,
      int? isReceive});
}

/// @nodoc
class __$$_TeamStudyActivityRewardCopyWithImpl<$Res>
    extends _$TeamStudyActivityRewardCopyWithImpl<$Res,
        _$_TeamStudyActivityReward>
    implements _$$_TeamStudyActivityRewardCopyWith<$Res> {
  __$$_TeamStudyActivityRewardCopyWithImpl(_$_TeamStudyActivityReward _value,
      $Res Function(_$_TeamStudyActivityReward) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = freezed,
    Object? type = freezed,
    Object? num = freezed,
    Object? name = freezed,
    Object? desc = freezed,
    Object? img = freezed,
    Object? isReceive = freezed,
  }) {
    return _then(_$_TeamStudyActivityReward(
      id: freezed == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as int?,
      type: freezed == type
          ? _value.type
          : type // ignore: cast_nullable_to_non_nullable
              as int?,
      num: freezed == num
          ? _value.num
          : num // ignore: cast_nullable_to_non_nullable
              as int?,
      name: freezed == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String?,
      desc: freezed == desc
          ? _value.desc
          : desc // ignore: cast_nullable_to_non_nullable
              as String?,
      img: freezed == img
          ? _value.img
          : img // ignore: cast_nullable_to_non_nullable
              as String?,
      isReceive: freezed == isReceive
          ? _value.isReceive
          : isReceive // ignore: cast_nullable_to_non_nullable
              as int?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_TeamStudyActivityReward implements _TeamStudyActivityReward {
  _$_TeamStudyActivityReward(
      {this.id,
      this.type,
      this.num,
      this.name,
      this.desc,
      this.img,
      this.isReceive});

  factory _$_TeamStudyActivityReward.fromJson(Map<String, dynamic> json) =>
      _$$_TeamStudyActivityRewardFromJson(json);

  @override
  final int? id;
  @override
  final int? type;
  @override
  final int? num;
  @override
  final String? name;
  @override
  final String? desc;
  @override
  final String? img;
  @override
  final int? isReceive;

  @override
  String toString() {
    return 'TeamStudyActivityReward(id: $id, type: $type, num: $num, name: $name, desc: $desc, img: $img, isReceive: $isReceive)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_TeamStudyActivityReward &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.type, type) || other.type == type) &&
            (identical(other.num, num) || other.num == num) &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.desc, desc) || other.desc == desc) &&
            (identical(other.img, img) || other.img == img) &&
            (identical(other.isReceive, isReceive) ||
                other.isReceive == isReceive));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode =>
      Object.hash(runtimeType, id, type, num, name, desc, img, isReceive);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_TeamStudyActivityRewardCopyWith<_$_TeamStudyActivityReward>
      get copyWith =>
          __$$_TeamStudyActivityRewardCopyWithImpl<_$_TeamStudyActivityReward>(
              this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_TeamStudyActivityRewardToJson(
      this,
    );
  }
}

abstract class _TeamStudyActivityReward implements TeamStudyActivityReward {
  factory _TeamStudyActivityReward(
      {final int? id,
      final int? type,
      final int? num,
      final String? name,
      final String? desc,
      final String? img,
      final int? isReceive}) = _$_TeamStudyActivityReward;

  factory _TeamStudyActivityReward.fromJson(Map<String, dynamic> json) =
      _$_TeamStudyActivityReward.fromJson;

  @override
  int? get id;
  @override
  int? get type;
  @override
  int? get num;
  @override
  String? get name;
  @override
  String? get desc;
  @override
  String? get img;
  @override
  int? get isReceive;
  @override
  @JsonKey(ignore: true)
  _$$_TeamStudyActivityRewardCopyWith<_$_TeamStudyActivityReward>
      get copyWith => throw _privateConstructorUsedError;
}

TeamStudyActivityMember _$TeamStudyActivityMemberFromJson(
    Map<String, dynamic> json) {
  return _TeamStudyActivityMember.fromJson(json);
}

/// @nodoc
mixin _$TeamStudyActivityMember {
  int? get memberId => throw _privateConstructorUsedError;
  String? get photo => throw _privateConstructorUsedError;
  String? get nickname => throw _privateConstructorUsedError;
  int? get dayCount => throw _privateConstructorUsedError;
  int? get isSelf => throw _privateConstructorUsedError;
  int? get pendingCollectionEnergy => throw _privateConstructorUsedError;
  List<TeamStudyActivityMemberDress>? get dressList =>
      throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $TeamStudyActivityMemberCopyWith<TeamStudyActivityMember> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $TeamStudyActivityMemberCopyWith<$Res> {
  factory $TeamStudyActivityMemberCopyWith(TeamStudyActivityMember value,
          $Res Function(TeamStudyActivityMember) then) =
      _$TeamStudyActivityMemberCopyWithImpl<$Res, TeamStudyActivityMember>;
  @useResult
  $Res call(
      {int? memberId,
      String? photo,
      String? nickname,
      int? dayCount,
      int? isSelf,
      int? pendingCollectionEnergy,
      List<TeamStudyActivityMemberDress>? dressList});
}

/// @nodoc
class _$TeamStudyActivityMemberCopyWithImpl<$Res,
        $Val extends TeamStudyActivityMember>
    implements $TeamStudyActivityMemberCopyWith<$Res> {
  _$TeamStudyActivityMemberCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? memberId = freezed,
    Object? photo = freezed,
    Object? nickname = freezed,
    Object? dayCount = freezed,
    Object? isSelf = freezed,
    Object? pendingCollectionEnergy = freezed,
    Object? dressList = freezed,
  }) {
    return _then(_value.copyWith(
      memberId: freezed == memberId
          ? _value.memberId
          : memberId // ignore: cast_nullable_to_non_nullable
              as int?,
      photo: freezed == photo
          ? _value.photo
          : photo // ignore: cast_nullable_to_non_nullable
              as String?,
      nickname: freezed == nickname
          ? _value.nickname
          : nickname // ignore: cast_nullable_to_non_nullable
              as String?,
      dayCount: freezed == dayCount
          ? _value.dayCount
          : dayCount // ignore: cast_nullable_to_non_nullable
              as int?,
      isSelf: freezed == isSelf
          ? _value.isSelf
          : isSelf // ignore: cast_nullable_to_non_nullable
              as int?,
      pendingCollectionEnergy: freezed == pendingCollectionEnergy
          ? _value.pendingCollectionEnergy
          : pendingCollectionEnergy // ignore: cast_nullable_to_non_nullable
              as int?,
      dressList: freezed == dressList
          ? _value.dressList
          : dressList // ignore: cast_nullable_to_non_nullable
              as List<TeamStudyActivityMemberDress>?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_TeamStudyActivityMemberCopyWith<$Res>
    implements $TeamStudyActivityMemberCopyWith<$Res> {
  factory _$$_TeamStudyActivityMemberCopyWith(_$_TeamStudyActivityMember value,
          $Res Function(_$_TeamStudyActivityMember) then) =
      __$$_TeamStudyActivityMemberCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {int? memberId,
      String? photo,
      String? nickname,
      int? dayCount,
      int? isSelf,
      int? pendingCollectionEnergy,
      List<TeamStudyActivityMemberDress>? dressList});
}

/// @nodoc
class __$$_TeamStudyActivityMemberCopyWithImpl<$Res>
    extends _$TeamStudyActivityMemberCopyWithImpl<$Res,
        _$_TeamStudyActivityMember>
    implements _$$_TeamStudyActivityMemberCopyWith<$Res> {
  __$$_TeamStudyActivityMemberCopyWithImpl(_$_TeamStudyActivityMember _value,
      $Res Function(_$_TeamStudyActivityMember) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? memberId = freezed,
    Object? photo = freezed,
    Object? nickname = freezed,
    Object? dayCount = freezed,
    Object? isSelf = freezed,
    Object? pendingCollectionEnergy = freezed,
    Object? dressList = freezed,
  }) {
    return _then(_$_TeamStudyActivityMember(
      memberId: freezed == memberId
          ? _value.memberId
          : memberId // ignore: cast_nullable_to_non_nullable
              as int?,
      photo: freezed == photo
          ? _value.photo
          : photo // ignore: cast_nullable_to_non_nullable
              as String?,
      nickname: freezed == nickname
          ? _value.nickname
          : nickname // ignore: cast_nullable_to_non_nullable
              as String?,
      dayCount: freezed == dayCount
          ? _value.dayCount
          : dayCount // ignore: cast_nullable_to_non_nullable
              as int?,
      isSelf: freezed == isSelf
          ? _value.isSelf
          : isSelf // ignore: cast_nullable_to_non_nullable
              as int?,
      pendingCollectionEnergy: freezed == pendingCollectionEnergy
          ? _value.pendingCollectionEnergy
          : pendingCollectionEnergy // ignore: cast_nullable_to_non_nullable
              as int?,
      dressList: freezed == dressList
          ? _value._dressList
          : dressList // ignore: cast_nullable_to_non_nullable
              as List<TeamStudyActivityMemberDress>?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_TeamStudyActivityMember implements _TeamStudyActivityMember {
  _$_TeamStudyActivityMember(
      {this.memberId,
      this.photo,
      this.nickname,
      this.dayCount,
      this.isSelf,
      this.pendingCollectionEnergy,
      final List<TeamStudyActivityMemberDress>? dressList})
      : _dressList = dressList;

  factory _$_TeamStudyActivityMember.fromJson(Map<String, dynamic> json) =>
      _$$_TeamStudyActivityMemberFromJson(json);

  @override
  final int? memberId;
  @override
  final String? photo;
  @override
  final String? nickname;
  @override
  final int? dayCount;
  @override
  final int? isSelf;
  @override
  final int? pendingCollectionEnergy;
  final List<TeamStudyActivityMemberDress>? _dressList;
  @override
  List<TeamStudyActivityMemberDress>? get dressList {
    final value = _dressList;
    if (value == null) return null;
    if (_dressList is EqualUnmodifiableListView) return _dressList;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  String toString() {
    return 'TeamStudyActivityMember(memberId: $memberId, photo: $photo, nickname: $nickname, dayCount: $dayCount, isSelf: $isSelf, pendingCollectionEnergy: $pendingCollectionEnergy, dressList: $dressList)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_TeamStudyActivityMember &&
            (identical(other.memberId, memberId) ||
                other.memberId == memberId) &&
            (identical(other.photo, photo) || other.photo == photo) &&
            (identical(other.nickname, nickname) ||
                other.nickname == nickname) &&
            (identical(other.dayCount, dayCount) ||
                other.dayCount == dayCount) &&
            (identical(other.isSelf, isSelf) || other.isSelf == isSelf) &&
            (identical(
                    other.pendingCollectionEnergy, pendingCollectionEnergy) ||
                other.pendingCollectionEnergy == pendingCollectionEnergy) &&
            const DeepCollectionEquality()
                .equals(other._dressList, _dressList));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      memberId,
      photo,
      nickname,
      dayCount,
      isSelf,
      pendingCollectionEnergy,
      const DeepCollectionEquality().hash(_dressList));

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_TeamStudyActivityMemberCopyWith<_$_TeamStudyActivityMember>
      get copyWith =>
          __$$_TeamStudyActivityMemberCopyWithImpl<_$_TeamStudyActivityMember>(
              this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_TeamStudyActivityMemberToJson(
      this,
    );
  }
}

abstract class _TeamStudyActivityMember implements TeamStudyActivityMember {
  factory _TeamStudyActivityMember(
          {final int? memberId,
          final String? photo,
          final String? nickname,
          final int? dayCount,
          final int? isSelf,
          final int? pendingCollectionEnergy,
          final List<TeamStudyActivityMemberDress>? dressList}) =
      _$_TeamStudyActivityMember;

  factory _TeamStudyActivityMember.fromJson(Map<String, dynamic> json) =
      _$_TeamStudyActivityMember.fromJson;

  @override
  int? get memberId;
  @override
  String? get photo;
  @override
  String? get nickname;
  @override
  int? get dayCount;
  @override
  int? get isSelf;
  @override
  int? get pendingCollectionEnergy;
  @override
  List<TeamStudyActivityMemberDress>? get dressList;
  @override
  @JsonKey(ignore: true)
  _$$_TeamStudyActivityMemberCopyWith<_$_TeamStudyActivityMember>
      get copyWith => throw _privateConstructorUsedError;
}

TeamStudyActivityMemberDress _$TeamStudyActivityMemberDressFromJson(
    Map<String, dynamic> json) {
  return _TeamStudyActivityMemberDress.fromJson(json);
}

/// @nodoc
mixin _$TeamStudyActivityMemberDress {
  String? get resource => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $TeamStudyActivityMemberDressCopyWith<TeamStudyActivityMemberDress>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $TeamStudyActivityMemberDressCopyWith<$Res> {
  factory $TeamStudyActivityMemberDressCopyWith(
          TeamStudyActivityMemberDress value,
          $Res Function(TeamStudyActivityMemberDress) then) =
      _$TeamStudyActivityMemberDressCopyWithImpl<$Res,
          TeamStudyActivityMemberDress>;
  @useResult
  $Res call({String? resource});
}

/// @nodoc
class _$TeamStudyActivityMemberDressCopyWithImpl<$Res,
        $Val extends TeamStudyActivityMemberDress>
    implements $TeamStudyActivityMemberDressCopyWith<$Res> {
  _$TeamStudyActivityMemberDressCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? resource = freezed,
  }) {
    return _then(_value.copyWith(
      resource: freezed == resource
          ? _value.resource
          : resource // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_TeamStudyActivityMemberDressCopyWith<$Res>
    implements $TeamStudyActivityMemberDressCopyWith<$Res> {
  factory _$$_TeamStudyActivityMemberDressCopyWith(
          _$_TeamStudyActivityMemberDress value,
          $Res Function(_$_TeamStudyActivityMemberDress) then) =
      __$$_TeamStudyActivityMemberDressCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String? resource});
}

/// @nodoc
class __$$_TeamStudyActivityMemberDressCopyWithImpl<$Res>
    extends _$TeamStudyActivityMemberDressCopyWithImpl<$Res,
        _$_TeamStudyActivityMemberDress>
    implements _$$_TeamStudyActivityMemberDressCopyWith<$Res> {
  __$$_TeamStudyActivityMemberDressCopyWithImpl(
      _$_TeamStudyActivityMemberDress _value,
      $Res Function(_$_TeamStudyActivityMemberDress) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? resource = freezed,
  }) {
    return _then(_$_TeamStudyActivityMemberDress(
      resource: freezed == resource
          ? _value.resource
          : resource // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_TeamStudyActivityMemberDress implements _TeamStudyActivityMemberDress {
  _$_TeamStudyActivityMemberDress({this.resource});

  factory _$_TeamStudyActivityMemberDress.fromJson(Map<String, dynamic> json) =>
      _$$_TeamStudyActivityMemberDressFromJson(json);

  @override
  final String? resource;

  @override
  String toString() {
    return 'TeamStudyActivityMemberDress(resource: $resource)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_TeamStudyActivityMemberDress &&
            (identical(other.resource, resource) ||
                other.resource == resource));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, resource);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_TeamStudyActivityMemberDressCopyWith<_$_TeamStudyActivityMemberDress>
      get copyWith => __$$_TeamStudyActivityMemberDressCopyWithImpl<
          _$_TeamStudyActivityMemberDress>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_TeamStudyActivityMemberDressToJson(
      this,
    );
  }
}

abstract class _TeamStudyActivityMemberDress
    implements TeamStudyActivityMemberDress {
  factory _TeamStudyActivityMemberDress({final String? resource}) =
      _$_TeamStudyActivityMemberDress;

  factory _TeamStudyActivityMemberDress.fromJson(Map<String, dynamic> json) =
      _$_TeamStudyActivityMemberDress.fromJson;

  @override
  String? get resource;
  @override
  @JsonKey(ignore: true)
  _$$_TeamStudyActivityMemberDressCopyWith<_$_TeamStudyActivityMemberDress>
      get copyWith => throw _privateConstructorUsedError;
}

TeamStudyResourceConfig _$TeamStudyResourceConfigFromJson(
    Map<String, dynamic> json) {
  return _TeamStudyResourceConfig.fromJson(json);
}

/// @nodoc
mixin _$TeamStudyResourceConfig {
  String? get topBgColor => throw _privateConstructorUsedError;
  String? get bottomBgColor => throw _privateConstructorUsedError;
  List<TeamStudySpineResource>? get animationLayers =>
      throw _privateConstructorUsedError;
  String? get boatStartTips => throw _privateConstructorUsedError;
  String? get teamStudyRecruitTitle => throw _privateConstructorUsedError;
  String? get teamStudyRecruitDetail => throw _privateConstructorUsedError;
  String? get teamStudyRecruitDetail2 => throw _privateConstructorUsedError;
  String? get teamStudyRecruitTipTwoLeft => throw _privateConstructorUsedError;
  String? get teamStudyRecruitTipOneLeft => throw _privateConstructorUsedError;
  String? get teamStudyRecruitTipComplete => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $TeamStudyResourceConfigCopyWith<TeamStudyResourceConfig> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $TeamStudyResourceConfigCopyWith<$Res> {
  factory $TeamStudyResourceConfigCopyWith(TeamStudyResourceConfig value,
          $Res Function(TeamStudyResourceConfig) then) =
      _$TeamStudyResourceConfigCopyWithImpl<$Res, TeamStudyResourceConfig>;
  @useResult
  $Res call(
      {String? topBgColor,
      String? bottomBgColor,
      List<TeamStudySpineResource>? animationLayers,
      String? boatStartTips,
      String? teamStudyRecruitTitle,
      String? teamStudyRecruitDetail,
      String? teamStudyRecruitDetail2,
      String? teamStudyRecruitTipTwoLeft,
      String? teamStudyRecruitTipOneLeft,
      String? teamStudyRecruitTipComplete});
}

/// @nodoc
class _$TeamStudyResourceConfigCopyWithImpl<$Res,
        $Val extends TeamStudyResourceConfig>
    implements $TeamStudyResourceConfigCopyWith<$Res> {
  _$TeamStudyResourceConfigCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? topBgColor = freezed,
    Object? bottomBgColor = freezed,
    Object? animationLayers = freezed,
    Object? boatStartTips = freezed,
    Object? teamStudyRecruitTitle = freezed,
    Object? teamStudyRecruitDetail = freezed,
    Object? teamStudyRecruitDetail2 = freezed,
    Object? teamStudyRecruitTipTwoLeft = freezed,
    Object? teamStudyRecruitTipOneLeft = freezed,
    Object? teamStudyRecruitTipComplete = freezed,
  }) {
    return _then(_value.copyWith(
      topBgColor: freezed == topBgColor
          ? _value.topBgColor
          : topBgColor // ignore: cast_nullable_to_non_nullable
              as String?,
      bottomBgColor: freezed == bottomBgColor
          ? _value.bottomBgColor
          : bottomBgColor // ignore: cast_nullable_to_non_nullable
              as String?,
      animationLayers: freezed == animationLayers
          ? _value.animationLayers
          : animationLayers // ignore: cast_nullable_to_non_nullable
              as List<TeamStudySpineResource>?,
      boatStartTips: freezed == boatStartTips
          ? _value.boatStartTips
          : boatStartTips // ignore: cast_nullable_to_non_nullable
              as String?,
      teamStudyRecruitTitle: freezed == teamStudyRecruitTitle
          ? _value.teamStudyRecruitTitle
          : teamStudyRecruitTitle // ignore: cast_nullable_to_non_nullable
              as String?,
      teamStudyRecruitDetail: freezed == teamStudyRecruitDetail
          ? _value.teamStudyRecruitDetail
          : teamStudyRecruitDetail // ignore: cast_nullable_to_non_nullable
              as String?,
      teamStudyRecruitDetail2: freezed == teamStudyRecruitDetail2
          ? _value.teamStudyRecruitDetail2
          : teamStudyRecruitDetail2 // ignore: cast_nullable_to_non_nullable
              as String?,
      teamStudyRecruitTipTwoLeft: freezed == teamStudyRecruitTipTwoLeft
          ? _value.teamStudyRecruitTipTwoLeft
          : teamStudyRecruitTipTwoLeft // ignore: cast_nullable_to_non_nullable
              as String?,
      teamStudyRecruitTipOneLeft: freezed == teamStudyRecruitTipOneLeft
          ? _value.teamStudyRecruitTipOneLeft
          : teamStudyRecruitTipOneLeft // ignore: cast_nullable_to_non_nullable
              as String?,
      teamStudyRecruitTipComplete: freezed == teamStudyRecruitTipComplete
          ? _value.teamStudyRecruitTipComplete
          : teamStudyRecruitTipComplete // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_TeamStudyResourceConfigCopyWith<$Res>
    implements $TeamStudyResourceConfigCopyWith<$Res> {
  factory _$$_TeamStudyResourceConfigCopyWith(_$_TeamStudyResourceConfig value,
          $Res Function(_$_TeamStudyResourceConfig) then) =
      __$$_TeamStudyResourceConfigCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String? topBgColor,
      String? bottomBgColor,
      List<TeamStudySpineResource>? animationLayers,
      String? boatStartTips,
      String? teamStudyRecruitTitle,
      String? teamStudyRecruitDetail,
      String? teamStudyRecruitDetail2,
      String? teamStudyRecruitTipTwoLeft,
      String? teamStudyRecruitTipOneLeft,
      String? teamStudyRecruitTipComplete});
}

/// @nodoc
class __$$_TeamStudyResourceConfigCopyWithImpl<$Res>
    extends _$TeamStudyResourceConfigCopyWithImpl<$Res,
        _$_TeamStudyResourceConfig>
    implements _$$_TeamStudyResourceConfigCopyWith<$Res> {
  __$$_TeamStudyResourceConfigCopyWithImpl(_$_TeamStudyResourceConfig _value,
      $Res Function(_$_TeamStudyResourceConfig) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? topBgColor = freezed,
    Object? bottomBgColor = freezed,
    Object? animationLayers = freezed,
    Object? boatStartTips = freezed,
    Object? teamStudyRecruitTitle = freezed,
    Object? teamStudyRecruitDetail = freezed,
    Object? teamStudyRecruitDetail2 = freezed,
    Object? teamStudyRecruitTipTwoLeft = freezed,
    Object? teamStudyRecruitTipOneLeft = freezed,
    Object? teamStudyRecruitTipComplete = freezed,
  }) {
    return _then(_$_TeamStudyResourceConfig(
      topBgColor: freezed == topBgColor
          ? _value.topBgColor
          : topBgColor // ignore: cast_nullable_to_non_nullable
              as String?,
      bottomBgColor: freezed == bottomBgColor
          ? _value.bottomBgColor
          : bottomBgColor // ignore: cast_nullable_to_non_nullable
              as String?,
      animationLayers: freezed == animationLayers
          ? _value._animationLayers
          : animationLayers // ignore: cast_nullable_to_non_nullable
              as List<TeamStudySpineResource>?,
      boatStartTips: freezed == boatStartTips
          ? _value.boatStartTips
          : boatStartTips // ignore: cast_nullable_to_non_nullable
              as String?,
      teamStudyRecruitTitle: freezed == teamStudyRecruitTitle
          ? _value.teamStudyRecruitTitle
          : teamStudyRecruitTitle // ignore: cast_nullable_to_non_nullable
              as String?,
      teamStudyRecruitDetail: freezed == teamStudyRecruitDetail
          ? _value.teamStudyRecruitDetail
          : teamStudyRecruitDetail // ignore: cast_nullable_to_non_nullable
              as String?,
      teamStudyRecruitDetail2: freezed == teamStudyRecruitDetail2
          ? _value.teamStudyRecruitDetail2
          : teamStudyRecruitDetail2 // ignore: cast_nullable_to_non_nullable
              as String?,
      teamStudyRecruitTipTwoLeft: freezed == teamStudyRecruitTipTwoLeft
          ? _value.teamStudyRecruitTipTwoLeft
          : teamStudyRecruitTipTwoLeft // ignore: cast_nullable_to_non_nullable
              as String?,
      teamStudyRecruitTipOneLeft: freezed == teamStudyRecruitTipOneLeft
          ? _value.teamStudyRecruitTipOneLeft
          : teamStudyRecruitTipOneLeft // ignore: cast_nullable_to_non_nullable
              as String?,
      teamStudyRecruitTipComplete: freezed == teamStudyRecruitTipComplete
          ? _value.teamStudyRecruitTipComplete
          : teamStudyRecruitTipComplete // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_TeamStudyResourceConfig implements _TeamStudyResourceConfig {
  _$_TeamStudyResourceConfig(
      {this.topBgColor,
      this.bottomBgColor,
      final List<TeamStudySpineResource>? animationLayers,
      this.boatStartTips,
      this.teamStudyRecruitTitle,
      this.teamStudyRecruitDetail,
      this.teamStudyRecruitDetail2,
      this.teamStudyRecruitTipTwoLeft,
      this.teamStudyRecruitTipOneLeft,
      this.teamStudyRecruitTipComplete})
      : _animationLayers = animationLayers;

  factory _$_TeamStudyResourceConfig.fromJson(Map<String, dynamic> json) =>
      _$$_TeamStudyResourceConfigFromJson(json);

  @override
  final String? topBgColor;
  @override
  final String? bottomBgColor;
  final List<TeamStudySpineResource>? _animationLayers;
  @override
  List<TeamStudySpineResource>? get animationLayers {
    final value = _animationLayers;
    if (value == null) return null;
    if (_animationLayers is EqualUnmodifiableListView) return _animationLayers;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  final String? boatStartTips;
  @override
  final String? teamStudyRecruitTitle;
  @override
  final String? teamStudyRecruitDetail;
  @override
  final String? teamStudyRecruitDetail2;
  @override
  final String? teamStudyRecruitTipTwoLeft;
  @override
  final String? teamStudyRecruitTipOneLeft;
  @override
  final String? teamStudyRecruitTipComplete;

  @override
  String toString() {
    return 'TeamStudyResourceConfig(topBgColor: $topBgColor, bottomBgColor: $bottomBgColor, animationLayers: $animationLayers, boatStartTips: $boatStartTips, teamStudyRecruitTitle: $teamStudyRecruitTitle, teamStudyRecruitDetail: $teamStudyRecruitDetail, teamStudyRecruitDetail2: $teamStudyRecruitDetail2, teamStudyRecruitTipTwoLeft: $teamStudyRecruitTipTwoLeft, teamStudyRecruitTipOneLeft: $teamStudyRecruitTipOneLeft, teamStudyRecruitTipComplete: $teamStudyRecruitTipComplete)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_TeamStudyResourceConfig &&
            (identical(other.topBgColor, topBgColor) ||
                other.topBgColor == topBgColor) &&
            (identical(other.bottomBgColor, bottomBgColor) ||
                other.bottomBgColor == bottomBgColor) &&
            const DeepCollectionEquality()
                .equals(other._animationLayers, _animationLayers) &&
            (identical(other.boatStartTips, boatStartTips) ||
                other.boatStartTips == boatStartTips) &&
            (identical(other.teamStudyRecruitTitle, teamStudyRecruitTitle) ||
                other.teamStudyRecruitTitle == teamStudyRecruitTitle) &&
            (identical(other.teamStudyRecruitDetail, teamStudyRecruitDetail) ||
                other.teamStudyRecruitDetail == teamStudyRecruitDetail) &&
            (identical(
                    other.teamStudyRecruitDetail2, teamStudyRecruitDetail2) ||
                other.teamStudyRecruitDetail2 == teamStudyRecruitDetail2) &&
            (identical(other.teamStudyRecruitTipTwoLeft,
                    teamStudyRecruitTipTwoLeft) ||
                other.teamStudyRecruitTipTwoLeft ==
                    teamStudyRecruitTipTwoLeft) &&
            (identical(other.teamStudyRecruitTipOneLeft,
                    teamStudyRecruitTipOneLeft) ||
                other.teamStudyRecruitTipOneLeft ==
                    teamStudyRecruitTipOneLeft) &&
            (identical(other.teamStudyRecruitTipComplete,
                    teamStudyRecruitTipComplete) ||
                other.teamStudyRecruitTipComplete ==
                    teamStudyRecruitTipComplete));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      topBgColor,
      bottomBgColor,
      const DeepCollectionEquality().hash(_animationLayers),
      boatStartTips,
      teamStudyRecruitTitle,
      teamStudyRecruitDetail,
      teamStudyRecruitDetail2,
      teamStudyRecruitTipTwoLeft,
      teamStudyRecruitTipOneLeft,
      teamStudyRecruitTipComplete);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_TeamStudyResourceConfigCopyWith<_$_TeamStudyResourceConfig>
      get copyWith =>
          __$$_TeamStudyResourceConfigCopyWithImpl<_$_TeamStudyResourceConfig>(
              this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_TeamStudyResourceConfigToJson(
      this,
    );
  }
}

abstract class _TeamStudyResourceConfig implements TeamStudyResourceConfig {
  factory _TeamStudyResourceConfig(
      {final String? topBgColor,
      final String? bottomBgColor,
      final List<TeamStudySpineResource>? animationLayers,
      final String? boatStartTips,
      final String? teamStudyRecruitTitle,
      final String? teamStudyRecruitDetail,
      final String? teamStudyRecruitDetail2,
      final String? teamStudyRecruitTipTwoLeft,
      final String? teamStudyRecruitTipOneLeft,
      final String? teamStudyRecruitTipComplete}) = _$_TeamStudyResourceConfig;

  factory _TeamStudyResourceConfig.fromJson(Map<String, dynamic> json) =
      _$_TeamStudyResourceConfig.fromJson;

  @override
  String? get topBgColor;
  @override
  String? get bottomBgColor;
  @override
  List<TeamStudySpineResource>? get animationLayers;
  @override
  String? get boatStartTips;
  @override
  String? get teamStudyRecruitTitle;
  @override
  String? get teamStudyRecruitDetail;
  @override
  String? get teamStudyRecruitDetail2;
  @override
  String? get teamStudyRecruitTipTwoLeft;
  @override
  String? get teamStudyRecruitTipOneLeft;
  @override
  String? get teamStudyRecruitTipComplete;
  @override
  @JsonKey(ignore: true)
  _$$_TeamStudyResourceConfigCopyWith<_$_TeamStudyResourceConfig>
      get copyWith => throw _privateConstructorUsedError;
}

TeamInvitationListResponse _$TeamInvitationListResponseFromJson(
    Map<String, dynamic> json) {
  return _TeamInvitationListResponse.fromJson(json);
}

/// @nodoc
mixin _$TeamInvitationListResponse {
  List<TeamInvitationUser>? get inviteList =>
      throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $TeamInvitationListResponseCopyWith<TeamInvitationListResponse>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $TeamInvitationListResponseCopyWith<$Res> {
  factory $TeamInvitationListResponseCopyWith(TeamInvitationListResponse value,
          $Res Function(TeamInvitationListResponse) then) =
      _$TeamInvitationListResponseCopyWithImpl<$Res,
          TeamInvitationListResponse>;
  @useResult
  $Res call({List<TeamInvitationUser>? inviteList});
}

/// @nodoc
class _$TeamInvitationListResponseCopyWithImpl<$Res,
        $Val extends TeamInvitationListResponse>
    implements $TeamInvitationListResponseCopyWith<$Res> {
  _$TeamInvitationListResponseCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? inviteList = freezed,
  }) {
    return _then(_value.copyWith(
      inviteList: freezed == inviteList
          ? _value.inviteList
          : inviteList // ignore: cast_nullable_to_non_nullable
              as List<TeamInvitationUser>?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_TeamInvitationListResponseCopyWith<$Res>
    implements $TeamInvitationListResponseCopyWith<$Res> {
  factory _$$_TeamInvitationListResponseCopyWith(
          _$_TeamInvitationListResponse value,
          $Res Function(_$_TeamInvitationListResponse) then) =
      __$$_TeamInvitationListResponseCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({List<TeamInvitationUser>? inviteList});
}

/// @nodoc
class __$$_TeamInvitationListResponseCopyWithImpl<$Res>
    extends _$TeamInvitationListResponseCopyWithImpl<$Res,
        _$_TeamInvitationListResponse>
    implements _$$_TeamInvitationListResponseCopyWith<$Res> {
  __$$_TeamInvitationListResponseCopyWithImpl(
      _$_TeamInvitationListResponse _value,
      $Res Function(_$_TeamInvitationListResponse) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? inviteList = freezed,
  }) {
    return _then(_$_TeamInvitationListResponse(
      inviteList: freezed == inviteList
          ? _value._inviteList
          : inviteList // ignore: cast_nullable_to_non_nullable
              as List<TeamInvitationUser>?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_TeamInvitationListResponse implements _TeamInvitationListResponse {
  _$_TeamInvitationListResponse({final List<TeamInvitationUser>? inviteList})
      : _inviteList = inviteList;

  factory _$_TeamInvitationListResponse.fromJson(Map<String, dynamic> json) =>
      _$$_TeamInvitationListResponseFromJson(json);

  final List<TeamInvitationUser>? _inviteList;
  @override
  List<TeamInvitationUser>? get inviteList {
    final value = _inviteList;
    if (value == null) return null;
    if (_inviteList is EqualUnmodifiableListView) return _inviteList;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  String toString() {
    return 'TeamInvitationListResponse(inviteList: $inviteList)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_TeamInvitationListResponse &&
            const DeepCollectionEquality()
                .equals(other._inviteList, _inviteList));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType, const DeepCollectionEquality().hash(_inviteList));

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_TeamInvitationListResponseCopyWith<_$_TeamInvitationListResponse>
      get copyWith => __$$_TeamInvitationListResponseCopyWithImpl<
          _$_TeamInvitationListResponse>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_TeamInvitationListResponseToJson(
      this,
    );
  }
}

abstract class _TeamInvitationListResponse
    implements TeamInvitationListResponse {
  factory _TeamInvitationListResponse(
          {final List<TeamInvitationUser>? inviteList}) =
      _$_TeamInvitationListResponse;

  factory _TeamInvitationListResponse.fromJson(Map<String, dynamic> json) =
      _$_TeamInvitationListResponse.fromJson;

  @override
  List<TeamInvitationUser>? get inviteList;
  @override
  @JsonKey(ignore: true)
  _$$_TeamInvitationListResponseCopyWith<_$_TeamInvitationListResponse>
      get copyWith => throw _privateConstructorUsedError;
}

TeamInvitationUser _$TeamInvitationUserFromJson(Map<String, dynamic> json) {
  return _TeamInvitationUser.fromJson(json);
}

/// @nodoc
mixin _$TeamInvitationUser {
  int? get userId => throw _privateConstructorUsedError; // 用户id
  String? get nickname => throw _privateConstructorUsedError; // 昵称
  String? get dressImg => throw _privateConstructorUsedError; // 装扮图
  int? get learnDay => throw _privateConstructorUsedError; // 累计学习天数
  int? get dayCount => throw _privateConstructorUsedError; // 当前连续天数
  int? get bestDayCount => throw _privateConstructorUsedError; // 历史连续天数
  int? get isInvite => throw _privateConstructorUsedError; // 是否邀请(1是0否)
  int? get isPatner => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $TeamInvitationUserCopyWith<TeamInvitationUser> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $TeamInvitationUserCopyWith<$Res> {
  factory $TeamInvitationUserCopyWith(
          TeamInvitationUser value, $Res Function(TeamInvitationUser) then) =
      _$TeamInvitationUserCopyWithImpl<$Res, TeamInvitationUser>;
  @useResult
  $Res call(
      {int? userId,
      String? nickname,
      String? dressImg,
      int? learnDay,
      int? dayCount,
      int? bestDayCount,
      int? isInvite,
      int? isPatner});
}

/// @nodoc
class _$TeamInvitationUserCopyWithImpl<$Res, $Val extends TeamInvitationUser>
    implements $TeamInvitationUserCopyWith<$Res> {
  _$TeamInvitationUserCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? userId = freezed,
    Object? nickname = freezed,
    Object? dressImg = freezed,
    Object? learnDay = freezed,
    Object? dayCount = freezed,
    Object? bestDayCount = freezed,
    Object? isInvite = freezed,
    Object? isPatner = freezed,
  }) {
    return _then(_value.copyWith(
      userId: freezed == userId
          ? _value.userId
          : userId // ignore: cast_nullable_to_non_nullable
              as int?,
      nickname: freezed == nickname
          ? _value.nickname
          : nickname // ignore: cast_nullable_to_non_nullable
              as String?,
      dressImg: freezed == dressImg
          ? _value.dressImg
          : dressImg // ignore: cast_nullable_to_non_nullable
              as String?,
      learnDay: freezed == learnDay
          ? _value.learnDay
          : learnDay // ignore: cast_nullable_to_non_nullable
              as int?,
      dayCount: freezed == dayCount
          ? _value.dayCount
          : dayCount // ignore: cast_nullable_to_non_nullable
              as int?,
      bestDayCount: freezed == bestDayCount
          ? _value.bestDayCount
          : bestDayCount // ignore: cast_nullable_to_non_nullable
              as int?,
      isInvite: freezed == isInvite
          ? _value.isInvite
          : isInvite // ignore: cast_nullable_to_non_nullable
              as int?,
      isPatner: freezed == isPatner
          ? _value.isPatner
          : isPatner // ignore: cast_nullable_to_non_nullable
              as int?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_TeamInvitationUserCopyWith<$Res>
    implements $TeamInvitationUserCopyWith<$Res> {
  factory _$$_TeamInvitationUserCopyWith(_$_TeamInvitationUser value,
          $Res Function(_$_TeamInvitationUser) then) =
      __$$_TeamInvitationUserCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {int? userId,
      String? nickname,
      String? dressImg,
      int? learnDay,
      int? dayCount,
      int? bestDayCount,
      int? isInvite,
      int? isPatner});
}

/// @nodoc
class __$$_TeamInvitationUserCopyWithImpl<$Res>
    extends _$TeamInvitationUserCopyWithImpl<$Res, _$_TeamInvitationUser>
    implements _$$_TeamInvitationUserCopyWith<$Res> {
  __$$_TeamInvitationUserCopyWithImpl(
      _$_TeamInvitationUser _value, $Res Function(_$_TeamInvitationUser) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? userId = freezed,
    Object? nickname = freezed,
    Object? dressImg = freezed,
    Object? learnDay = freezed,
    Object? dayCount = freezed,
    Object? bestDayCount = freezed,
    Object? isInvite = freezed,
    Object? isPatner = freezed,
  }) {
    return _then(_$_TeamInvitationUser(
      userId: freezed == userId
          ? _value.userId
          : userId // ignore: cast_nullable_to_non_nullable
              as int?,
      nickname: freezed == nickname
          ? _value.nickname
          : nickname // ignore: cast_nullable_to_non_nullable
              as String?,
      dressImg: freezed == dressImg
          ? _value.dressImg
          : dressImg // ignore: cast_nullable_to_non_nullable
              as String?,
      learnDay: freezed == learnDay
          ? _value.learnDay
          : learnDay // ignore: cast_nullable_to_non_nullable
              as int?,
      dayCount: freezed == dayCount
          ? _value.dayCount
          : dayCount // ignore: cast_nullable_to_non_nullable
              as int?,
      bestDayCount: freezed == bestDayCount
          ? _value.bestDayCount
          : bestDayCount // ignore: cast_nullable_to_non_nullable
              as int?,
      isInvite: freezed == isInvite
          ? _value.isInvite
          : isInvite // ignore: cast_nullable_to_non_nullable
              as int?,
      isPatner: freezed == isPatner
          ? _value.isPatner
          : isPatner // ignore: cast_nullable_to_non_nullable
              as int?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_TeamInvitationUser implements _TeamInvitationUser {
  _$_TeamInvitationUser(
      {this.userId,
      this.nickname,
      this.dressImg,
      this.learnDay,
      this.dayCount,
      this.bestDayCount,
      this.isInvite,
      this.isPatner});

  factory _$_TeamInvitationUser.fromJson(Map<String, dynamic> json) =>
      _$$_TeamInvitationUserFromJson(json);

  @override
  final int? userId;
// 用户id
  @override
  final String? nickname;
// 昵称
  @override
  final String? dressImg;
// 装扮图
  @override
  final int? learnDay;
// 累计学习天数
  @override
  final int? dayCount;
// 当前连续天数
  @override
  final int? bestDayCount;
// 历史连续天数
  @override
  final int? isInvite;
// 是否邀请(1是0否)
  @override
  final int? isPatner;

  @override
  String toString() {
    return 'TeamInvitationUser(userId: $userId, nickname: $nickname, dressImg: $dressImg, learnDay: $learnDay, dayCount: $dayCount, bestDayCount: $bestDayCount, isInvite: $isInvite, isPatner: $isPatner)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_TeamInvitationUser &&
            (identical(other.userId, userId) || other.userId == userId) &&
            (identical(other.nickname, nickname) ||
                other.nickname == nickname) &&
            (identical(other.dressImg, dressImg) ||
                other.dressImg == dressImg) &&
            (identical(other.learnDay, learnDay) ||
                other.learnDay == learnDay) &&
            (identical(other.dayCount, dayCount) ||
                other.dayCount == dayCount) &&
            (identical(other.bestDayCount, bestDayCount) ||
                other.bestDayCount == bestDayCount) &&
            (identical(other.isInvite, isInvite) ||
                other.isInvite == isInvite) &&
            (identical(other.isPatner, isPatner) ||
                other.isPatner == isPatner));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, userId, nickname, dressImg,
      learnDay, dayCount, bestDayCount, isInvite, isPatner);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_TeamInvitationUserCopyWith<_$_TeamInvitationUser> get copyWith =>
      __$$_TeamInvitationUserCopyWithImpl<_$_TeamInvitationUser>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_TeamInvitationUserToJson(
      this,
    );
  }
}

abstract class _TeamInvitationUser implements TeamInvitationUser {
  factory _TeamInvitationUser(
      {final int? userId,
      final String? nickname,
      final String? dressImg,
      final int? learnDay,
      final int? dayCount,
      final int? bestDayCount,
      final int? isInvite,
      final int? isPatner}) = _$_TeamInvitationUser;

  factory _TeamInvitationUser.fromJson(Map<String, dynamic> json) =
      _$_TeamInvitationUser.fromJson;

  @override
  int? get userId;
  @override // 用户id
  String? get nickname;
  @override // 昵称
  String? get dressImg;
  @override // 装扮图
  int? get learnDay;
  @override // 累计学习天数
  int? get dayCount;
  @override // 当前连续天数
  int? get bestDayCount;
  @override // 历史连续天数
  int? get isInvite;
  @override // 是否邀请(1是0否)
  int? get isPatner;
  @override
  @JsonKey(ignore: true)
  _$$_TeamInvitationUserCopyWith<_$_TeamInvitationUser> get copyWith =>
      throw _privateConstructorUsedError;
}

TeamStudySpineResource _$TeamStudySpineResourceFromJson(
    Map<String, dynamic> json) {
  return _TeamStudySpineResource.fromJson(json);
}

/// @nodoc
mixin _$TeamStudySpineResource {
  int? get type => throw _privateConstructorUsedError;
  String? get name => throw _privateConstructorUsedError;
  List<String>? get personMount => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $TeamStudySpineResourceCopyWith<TeamStudySpineResource> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $TeamStudySpineResourceCopyWith<$Res> {
  factory $TeamStudySpineResourceCopyWith(TeamStudySpineResource value,
          $Res Function(TeamStudySpineResource) then) =
      _$TeamStudySpineResourceCopyWithImpl<$Res, TeamStudySpineResource>;
  @useResult
  $Res call({int? type, String? name, List<String>? personMount});
}

/// @nodoc
class _$TeamStudySpineResourceCopyWithImpl<$Res,
        $Val extends TeamStudySpineResource>
    implements $TeamStudySpineResourceCopyWith<$Res> {
  _$TeamStudySpineResourceCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? type = freezed,
    Object? name = freezed,
    Object? personMount = freezed,
  }) {
    return _then(_value.copyWith(
      type: freezed == type
          ? _value.type
          : type // ignore: cast_nullable_to_non_nullable
              as int?,
      name: freezed == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String?,
      personMount: freezed == personMount
          ? _value.personMount
          : personMount // ignore: cast_nullable_to_non_nullable
              as List<String>?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_TeamStudySpineResourceCopyWith<$Res>
    implements $TeamStudySpineResourceCopyWith<$Res> {
  factory _$$_TeamStudySpineResourceCopyWith(_$_TeamStudySpineResource value,
          $Res Function(_$_TeamStudySpineResource) then) =
      __$$_TeamStudySpineResourceCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({int? type, String? name, List<String>? personMount});
}

/// @nodoc
class __$$_TeamStudySpineResourceCopyWithImpl<$Res>
    extends _$TeamStudySpineResourceCopyWithImpl<$Res,
        _$_TeamStudySpineResource>
    implements _$$_TeamStudySpineResourceCopyWith<$Res> {
  __$$_TeamStudySpineResourceCopyWithImpl(_$_TeamStudySpineResource _value,
      $Res Function(_$_TeamStudySpineResource) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? type = freezed,
    Object? name = freezed,
    Object? personMount = freezed,
  }) {
    return _then(_$_TeamStudySpineResource(
      type: freezed == type
          ? _value.type
          : type // ignore: cast_nullable_to_non_nullable
              as int?,
      name: freezed == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String?,
      personMount: freezed == personMount
          ? _value._personMount
          : personMount // ignore: cast_nullable_to_non_nullable
              as List<String>?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_TeamStudySpineResource implements _TeamStudySpineResource {
  _$_TeamStudySpineResource(
      {this.type, this.name, final List<String>? personMount})
      : _personMount = personMount;

  factory _$_TeamStudySpineResource.fromJson(Map<String, dynamic> json) =>
      _$$_TeamStudySpineResourceFromJson(json);

  @override
  final int? type;
  @override
  final String? name;
  final List<String>? _personMount;
  @override
  List<String>? get personMount {
    final value = _personMount;
    if (value == null) return null;
    if (_personMount is EqualUnmodifiableListView) return _personMount;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  String toString() {
    return 'TeamStudySpineResource(type: $type, name: $name, personMount: $personMount)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_TeamStudySpineResource &&
            (identical(other.type, type) || other.type == type) &&
            (identical(other.name, name) || other.name == name) &&
            const DeepCollectionEquality()
                .equals(other._personMount, _personMount));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, type, name,
      const DeepCollectionEquality().hash(_personMount));

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_TeamStudySpineResourceCopyWith<_$_TeamStudySpineResource> get copyWith =>
      __$$_TeamStudySpineResourceCopyWithImpl<_$_TeamStudySpineResource>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_TeamStudySpineResourceToJson(
      this,
    );
  }
}

abstract class _TeamStudySpineResource implements TeamStudySpineResource {
  factory _TeamStudySpineResource(
      {final int? type,
      final String? name,
      final List<String>? personMount}) = _$_TeamStudySpineResource;

  factory _TeamStudySpineResource.fromJson(Map<String, dynamic> json) =
      _$_TeamStudySpineResource.fromJson;

  @override
  int? get type;
  @override
  String? get name;
  @override
  List<String>? get personMount;
  @override
  @JsonKey(ignore: true)
  _$$_TeamStudySpineResourceCopyWith<_$_TeamStudySpineResource> get copyWith =>
      throw _privateConstructorUsedError;
}
