import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/widgets.dart';
import 'package:jojo_flutter_base/base.dart';
import 'package:jojo_flutter_plan_pkg/pages/team_study_activity_page/models/team_study_activity_model.dart';
import 'package:jojo_flutter_plan_pkg/pages/team_study_activity_page/views/team_study_recruit/team_study_map_task_view.dart';
import 'package:jojo_flutter_plan_pkg/pages/team_study_activity_page/views/team_study_recruit/team_study_recruit_view.dart';
import 'package:jojo_flutter_plan_pkg/pages/team_study_activity_page/controller.dart';
import 'package:jojo_flutter_plan_pkg/pages/team_study_activity_page/state.dart';
import 'package:jojo_flutter_plan_pkg/pages/team_study_activity_page/views/boat_start_tip_view.dart';
import 'package:jojo_flutter_plan_pkg/pages/team_study_activity_page/views/find_treasure_box_tip_view.dart';

import 'package:jojo_flutter_plan_pkg/pages/team_study_activity_page/views/spine_views/team_study_animation_view.dart';

class TeamStudyActivityMainView extends StatelessWidget {
  final TeamStudyActivityState state;
  final TeamStudyActivityController controller;
  const TeamStudyActivityMainView(
      {super.key, required this.state, required this.controller});

  @override
  Widget build(BuildContext context) {
    return Stack(
      clipBehavior: Clip.none,
      children: [
        TeamStudyAnimationView(
          state: state,
          controller: controller,
        ), // 海层
        _uiLayer(context), // UI层
      ],
    );
  }

  _uiLayer(BuildContext context) {
    if (kDebugMode) {
      return Positioned.fill(
        child: Stack(
          children: [
            TeamStudyRecruitWidget(state: state, finishCallback: () => {controller.stepCompleted()}, onAddMemberTap: () {
            TeamStudyRecruitWidget.showInvitePartnerDialog(controller);
          }),
          ]
        )
      );
    }
    return Positioned.fill(
      child: Stack(
        children: [
          if (state.stepType == TeamStudyActivityStepType.shipStart)
            BoatStartTipView(
                audioName: TeamStudyActivityStepType.shipStart.audioName,
                finishCallback: () => {controller.stepCompleted()}),
          if (state.stepType == TeamStudyActivityStepType.findTreasure)
            FindTreasureBoxTipView(onTap: () => {controller.stepCompleted()}),
          if (state.stepType == TeamStudyActivityStepType.addPartner)
            TeamStudyRecruitWidget(state: state, finishCallback: () => {controller.stepCompleted()}, onAddMemberTap: () {
            TeamStudyRecruitWidget.showInvitePartnerDialog(controller);
          }),
          if (state.activityModel.inProgress()) // 2 进行中，后期改枚举吧
            TeamStudyMapTaskView(
              state: state, subjectType: null, topOffset: 520.rdp),
        ],
      ),
    );
  }
}
 