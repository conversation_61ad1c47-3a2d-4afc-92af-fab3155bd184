import 'dart:io';
import 'dart:math';
import 'package:flutter/material.dart';
import 'package:jojo_flutter_base/base.dart';
import 'package:jojo_flutter_plan_pkg/pages/plan_home_map/incentive_module/home_incentive_info_widget.dart';

class TeamCompleteSuccessDialog extends StatefulWidget {
  final String? localImagePath; // 从外部传入的本地图片路径
  final VoidCallback? onStartJourney; // 出发按钮回调
  final String detailTitle; // 一起去探索知识的海洋吧！

  const TeamCompleteSuccessDialog({
    super.key,
    this.localImagePath,
    this.onStartJourney,
    this.detailTitle = '一起去探索知识的海洋吧！',
  });

  @override
  State<TeamCompleteSuccessDialog> createState() => _TeamCompleteSuccessDialogState();

  /// 显示队伍集结成功弹窗的静态方法
  static void show({
    String? localImagePath,
    VoidCallback? onStartJourney,
  }) {
    SmartDialog.show(
      builder: (context) => TeamCompleteSuccessDialog(
        localImagePath: localImagePath,
        onStartJourney: onStartJourney,
      ),
      alignment: Alignment.center,
      maskColor: Colors.transparent,
      clickMaskDismiss: false,
      backDismiss: false,
      usePenetrate: false,
    );
  }
}

class _TeamCompleteSuccessDialogState extends State<TeamCompleteSuccessDialog>
    with TickerProviderStateMixin {
  late AnimationController _slideController;
  late AnimationController _fadeController;
  late Animation<Offset> _slideAnimation;
  late Animation<double> _fadeAnimation;

  @override
  void initState() {
    super.initState();
    _initAnimations();
    _startAnimations();
  }

  void _initAnimations() {
    // 滑入动画控制器
    _slideController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    // 淡入动画控制器
    _fadeController = AnimationController(
      duration: const Duration(milliseconds: 600),
      vsync: this,
    );

    // 从下往上滑入
    _slideAnimation = Tween<Offset>(
      begin: const Offset(0.0, 1.0),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _slideController,
      curve: Curves.easeOutCubic,
    ));

    // 淡入动画
    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _fadeController,
      curve: Curves.easeIn,
    ));
  }

  void _startAnimations() {
    _slideController.forward();
    Future.delayed(const Duration(milliseconds: 200), () {
      _fadeController.forward();
    });
  }

  @override
  void dispose() {
    _slideController.dispose();
    _fadeController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Material(
      color: hexToColor('#FEF4B6'),// Colors.transparent,
      child: Container(
        width: double.infinity,
        height: double.infinity,
        // color: ,
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              Color(0x00FFFFFF), // 浅黄色
              Colors.white60, // 金黄色
            ],
          ),
        ),
        child: SafeArea(
          child: SlideTransition(
            position: _slideAnimation,
            child: FadeTransition(
              opacity: _fadeAnimation,
              child: Stack(
                children: [
                  // 主要内容
                  Padding(
                    padding: EdgeInsets.symmetric(horizontal: 20.rdp),
                    child: Column(
                      children: [
                        _buildHeader(),
                        Expanded(child: _buildMapImage()),
                        SizedBox(height: 90.rdp), // 为底部按钮预留空间
                      ],
                    ),
                  ),
                  // 浮动按钮
                  Positioned(
                    left: 0,
                    right: 0,
                    bottom: 40.rdp,
                    child: Center(
                      child: SizedBox(
                        height: 44.rdp,
                        width: 280.rdp,
                        child: _buildBottomButton(),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      margin: EdgeInsets.only(top: 20.rdp),
      child: Column(
        children: [
          // 成功标题背景
          Container(
            width: 330.rdp,
            height: 80.rdp,
            decoration: BoxDecoration(
              color: const Color(0xFFFF6B35), // 橙红色背景
              borderRadius: BorderRadius.circular(20.rdp),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.1),
                  blurRadius: 8.rdp,
                  offset: Offset(0, 4.rdp),
                ),
              ],
            ),
          ),
          SizedBox(height: 12.rdp),
          // 副标题
          Text(
            widget.detailTitle,
            style: context.textstyles.headingEmphasis.pf.copyWith(color: context.appColors.jColorYellow6),
          ),
        ],
      ),
    );
  }

  // Widget _buildMapContent() {
  //   return Container(
  //     margin: EdgeInsets.only(top: 20.rdp),
  //     padding: EdgeInsets.symmetric(horizontal: 20.rdp),
  //     decoration: BoxDecoration(
  //       borderRadius: BorderRadius.circular(16.rdp),
  //       border: Border.all(
  //         color: const Color(0xFF9C27B0), // 紫色边框
  //         width: 3.rdp,
  //       ),
  //       boxShadow: [
  //         BoxShadow(
  //           color: Colors.black.withOpacity(0.1),
  //           blurRadius: 12.rdp,
  //           offset: Offset(0, 6.rdp),
  //         ),
  //       ],
  //     ),
  //     child: ClipRRect(
  //       borderRadius: BorderRadius.circular(13.rdp),
  //       child: _buildMapImage(),
  //     ),
  //   );
  // }

  Widget _buildMapImage() {
    return Center(
      child: AspectRatio(
        aspectRatio: 335 / 620, // 保持 335:620 的比例
        child: Container(
          width: 335.rdp,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(12.rdp),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.1),
                blurRadius: 8.rdp,
                offset: Offset(0, 4.rdp),
              ),
            ],
          ),
          child: ClipRRect(
            borderRadius: BorderRadius.circular(12.rdp),
            child: _buildImageContent(),
          ),
        ),
      ),
    );
  }

  Widget _buildImageContent() {
    if (widget.localImagePath != null && widget.localImagePath!.isNotEmpty) {
      // 显示本地下载的图片
      final file = File(widget.localImagePath!);
      if (file.existsSync()) {
        return Image.file(
          file,
          fit: BoxFit.cover,
        );
      }
    }

    // 显示默认占位图
    return Container(
      color: const Color(0xFF87CEEB), // 天蓝色背景
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.map,
            size: 80.rdp,
            color: Colors.white.withOpacity(0.7),
          ),
          SizedBox(height: 16.rdp),
          Text(
            '海洋地图加载中...',
            style: TextStyle(
              fontSize: 16.rdp,
              color: Colors.white,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildBottomButton() {
    return GestureDetector(
      onTap: () {
        SmartDialog.dismiss();
        widget.onStartJourney?.call();
      },
      child: Container(
        width: double.infinity,
        height: 50.rdp,
        decoration: BoxDecoration(
          color: const Color(0xFFFFD700), // 金黄色
          borderRadius: BorderRadius.circular(25.rdp),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.2),
              blurRadius: 8.rdp,
              offset: Offset(0, 4.rdp),
            ),
          ],
        ),
        child: Center(
          child: Text(
            '出发',
            style: TextStyle(
              fontSize: 18.rdp,
              fontWeight: FontWeight.bold,
              color: const Color(0xFF8B4513), // 棕色文字
            ),
          ),
        ),
      ),
    );
  }
}
