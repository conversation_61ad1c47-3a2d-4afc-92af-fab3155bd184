import 'package:flutter/material.dart';
import 'package:jojo_flutter_plan_pkg/service/team_study_activity_api.dart';
import 'package:jojo_flutter_plan_pkg/pages/team_study_activity_page/models/team_study_activity_model.dart';

/// 队伍邀请列表使用示例
class TeamInvitationExample extends StatefulWidget {
  final int teamId;

  const TeamInvitationExample({
    super.key,
    required this.teamId,
  });

  @override
  State<TeamInvitationExample> createState() => _TeamInvitationExampleState();
}

class _TeamInvitationExampleState extends State<TeamInvitationExample> {
  List<TeamInvitationUser> inviteList = [];
  bool isLoading = false;
  String? errorMessage;

  @override
  void initState() {
    super.initState();
    _loadInvitations();
  }

  /// 加载邀请列表
  Future<void> _loadInvitations() async {
    setState(() {
      isLoading = true;
      errorMessage = null;
    });

    try {
      // 调用新的邀请列表接口
      final response = await teamStudyApi.getTeamInvitations(widget.teamId);
      setState(() {
        inviteList = response.inviteList ?? [];
        isLoading = false;
      });
    } catch (e) {
      setState(() {
        errorMessage = '加载邀请列表失败: $e';
        isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('队伍邀请列表 (ID: ${widget.teamId})'),
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadInvitations,
          ),
        ],
      ),
      body: _buildBody(),
    );
  }

  Widget _buildBody() {
    if (isLoading) {
      return const Center(
        child: CircularProgressIndicator(),
      );
    }

    if (errorMessage != null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text(
              errorMessage!,
              style: const TextStyle(color: Colors.red),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: _loadInvitations,
              child: const Text('重试'),
            ),
          ],
        ),
      );
    }

    if (inviteList.isEmpty) {
      return const Center(
        child: Text('暂无邀请用户'),
      );
    }

    return ListView.builder(
      itemCount: inviteList.length,
      itemBuilder: (context, index) {
        final user = inviteList[index];
        return _buildUserCard(user);
      },
    );
  }

  Widget _buildUserCard(TeamInvitationUser user) {
    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                // 装扮图
                Container(
                  width: 50,
                  height: 50,
                  decoration: BoxDecoration(
                    color: Colors.grey[300],
                    borderRadius: BorderRadius.circular(25),
                  ),
                  child: user.dressImg?.isNotEmpty == true
                      ? ClipRRect(
                          borderRadius: BorderRadius.circular(25),
                          child: Image.network(
                            user.dressImg!,
                            fit: BoxFit.cover,
                            errorBuilder: (context, error, stackTrace) {
                              return const Icon(Icons.person);
                            },
                          ),
                        )
                      : const Icon(Icons.person),
                ),
                const SizedBox(width: 12),
                // 用户信息
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        user.nickname ?? '未知用户',
                        style: const TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      Text('用户ID: ${user.userId}'),
                    ],
                  ),
                ),
                // 状态标签
                Column(
                  children: [
                    if (user.isInvite == 1)
                      Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 8,
                          vertical: 4,
                        ),
                        decoration: BoxDecoration(
                          color: Colors.blue,
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: const Text(
                          '已邀请',
                          style: TextStyle(
                            color: Colors.white,
                            fontSize: 12,
                          ),
                        ),
                      ),
                    if (user.isPatner == 1)
                      Container(
                        margin: const EdgeInsets.only(top: 4),
                        padding: const EdgeInsets.symmetric(
                          horizontal: 8,
                          vertical: 4,
                        ),
                        decoration: BoxDecoration(
                          color: Colors.green,
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: const Text(
                          '学伴',
                          style: TextStyle(
                            color: Colors.white,
                            fontSize: 12,
                          ),
                        ),
                      ),
                  ],
                ),
              ],
            ),
            const SizedBox(height: 12),
            // 学习统计
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceAround,
              children: [
                _buildStatItem('累计学习', '${user.learnDay ?? 0}天'),
                _buildStatItem('当前连续', '${user.dayCount ?? 0}天'),
                _buildStatItem('历史最佳', '${user.bestDayCount ?? 0}天'),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatItem(String label, String value) {
    return Column(
      children: [
        Text(
          value,
          style: const TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
            color: Colors.blue,
          ),
        ),
        Text(
          label,
          style: const TextStyle(
            fontSize: 12,
            color: Colors.grey,
          ),
        ),
      ],
    );
  }
}
