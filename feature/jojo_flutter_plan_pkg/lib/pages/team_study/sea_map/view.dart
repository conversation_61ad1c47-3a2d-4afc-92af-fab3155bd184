import 'dart:io';

import 'package:flutter/material.dart';
import 'package:jojo_flutter_base/base.dart';
import 'package:jojo_flutter_base/spine/jojo_spine_animation_control.dart';
import 'package:jojo_flutter_base/utils/hex_color.dart';
import 'package:jojo_flutter_base/widgets/app_bars/jojo_appbar.dart';
import 'package:jojo_flutter_plan_pkg/pages/treasure_chest_page/treasure_chest_page.dart';

import '../../treasure_chest_page/treasure_chest_state.dart';
import 'controller.dart';
import 'model/team_study_sea_map_model.dart';
import 'state.dart';
import 'widgets/team_study_map_entry_builder.dart';
import 'widgets/team_study_map_point_builder.dart';

class TeamStudySeaMapView extends StatefulWidget {
  final TeamStudySeaMapState state;

  const TeamStudySeaMapView({super.key, required this.state});

  @override
  State<TeamStudySeaMapView> createState() => TeamStudySeaMapViewState();
}

class TeamStudySeaMapViewState extends State<TeamStudySeaMapView>
    with TickerProviderStateMixin {
  late TeamStudySeaMapController _controller;

  /// 滚动变化导航条颜色
  final double _barOpacityOffset = 100.0;
  late ValueNotifier<Color> _appBarColorNotifier;
  late Color startColor;

  Map<int, double> mapBgHeight = {};
  double mapScale = 0;
  final double _bouncingOffset = 350; // 防止多层ListView滑动时其他层触发回弹
  final double cacheOffset = 20;
  final double topCacheOffset = 20;

  late ScrollController _bgController; // 背景层
  late ScrollController _pointController; // 节点层
  late ScrollController _rewardController; // 奖励/交互层
  late AnimationController shakeController; // 可领取奖励-宝箱摇晃动画
  late AnimationController rotationController; // 可领取奖励-背景光旋转动画

  final JoJoSpineAnimationController spineController =
      JoJoSpineAnimationController();
  Widget? boatWidget;

  @override
  void initState() {
    super.initState();

    startColor = HexColor(widget.state.mapConfig?.barColor ?? "0x98F4F4");
    var endColor = Colors.white;
    _appBarColorNotifier = ValueNotifier(startColor);

    _controller = context.read<TeamStudySeaMapController>();
    _pointController = ScrollController(
        initialScrollOffset: _bouncingOffset - topCacheOffset.rdp);
    _bgController = ScrollController(
        initialScrollOffset: _bouncingOffset - topCacheOffset.rdp);
    _rewardController = ScrollController();

    var bgImages = widget.state.mapConfig?.bgImages ?? [];
    for (int i = 0; i < bgImages.length; i++) {
      mapBgHeight[i] = bgImages[i].rect?.h ?? 0;
    }

    _rewardController.addListener(() {
      // 背景层/节点层同步滚动
      _bgController.jumpTo(
          _rewardController.offset + _bouncingOffset.rdp - topCacheOffset.rdp);
      _pointController.jumpTo(
          _rewardController.offset + _bouncingOffset.rdp - topCacheOffset.rdp);

      // 计算新的bar色值
      double offset = _rewardController.offset;
      double ratio = (offset / _barOpacityOffset).clamp(0.0, 1.0);
      var newColor = Color.lerp(startColor, endColor, ratio)!;
      if (_appBarColorNotifier.value != newColor) {
        _appBarColorNotifier.value = newColor;
      }
    });

    pointsBuilderInit();

    WidgetsBinding.instance.addPostFrameCallback((_) {
      _scrollToInitialPoint();
    });
  }

  @override
  void dispose() {
    _appBarColorNotifier.dispose();
    _bgController.dispose();
    _pointController.dispose();
    super.dispose();
  }

  void rewardClick(
    TeamStudySeaMapPoint point,
    TeamStudySeaMapPointState pointState,
  ) {
    final data = widget.state.getPointRewardData(pointState);
    if (data.isEmpty) {
      return;
    }
    TreasureChestPage.show(data: data.first, onClose: () {});
    _controller.openReward(point, pointState);
  }

  _scrollToInitialPoint() {
    var point = widget.state.getCurrentPoint();
    var pointCenter = mapFit(point.rect?.y ?? 0 + (point.rect?.h ?? 0) / 2);
    var halfScreenHeight = MediaQuery.of(context).size.height / 2;
    var maxOffset = mapFit(widget.state.mapConfig?.bgHeight ?? 0) -
        MediaQuery.of(context).size.height +
        cacheOffset.rdp +
        topCacheOffset.rdp;
    var offset = (pointCenter - halfScreenHeight + 60.rdp).clamp(0, maxOffset);
    _rewardController.jumpTo(offset.toDouble());
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: PreferredSize(
        preferredSize: Size.fromHeight(
          const JoJoAppBar().preferredSize.height,
        ),
        child: ValueListenableBuilder<Color>(
          valueListenable: _appBarColorNotifier,
          builder: (context, color, child) {
            return JoJoAppBar(
              title: widget.state.mapConfig?.title ?? "",
              backgroundColor: color,
              centerTitle: true,
            );
          },
        ),
      ),
      body: Container(
        color: startColor,
        child: Stack(
          children: [
            ///背景图层
            Positioned.fill(
              top: -topCacheOffset.rdp,
              bottom: -cacheOffset.rdp,
              child: IgnorePointer(child: getBgListView()),
            ),
            // 节点图层
            Positioned.fill(
              top: -topCacheOffset.rdp,
              bottom: -cacheOffset.rdp,
              child: IgnorePointer(child: getPointListView()),
            ),
            // 奖励交互图层
            Positioned.fill(
              top: -topCacheOffset.rdp,
              bottom: -cacheOffset.rdp, // 可以让ListView在视角外加载下一组内容
              child: getRewardListView(),
            ),
          ],
        ),
      ),
    );
  }

  /// 背景图层
  Widget getBgListView() {
    var count = widget.state.mapConfig?.bgImages?.length ?? 0;
    return ListView.builder(
      controller: _bgController,
      physics: const NeverScrollableScrollPhysics(),
      itemCount: count + 2,
      itemBuilder: (context, index) {
        return getMapBgWidget(context, index);
      },
    );
  }

  /// 节点图层
  Widget getPointListView() {
    var groupHeights = widget.state.pointLayerGroupHeights ?? [];
    return ListView.builder(
      controller: _pointController,
      physics: const NeverScrollableScrollPhysics(),
      itemCount: groupHeights.length + 2,
      itemBuilder: (context, index) {
        return getMapPointsWidget(context, index);
      },
    );
  }

  /// 奖励/交互图层
  Widget getRewardListView() {
    var groupHeights = widget.state.rewardLayerGroupHeights ?? [];
    return ListView.builder(
      controller: _rewardController,
      physics: const BouncingScrollPhysics(),
      itemCount: groupHeights.length + 2,
      itemBuilder: (context, index) {
        return getMapRewardWidget(context, index);
      },
    );
  }

  /// 获取背景图
  Widget getMapBgWidget(BuildContext context, int index) {
    var mapModels = widget.state.mapConfig?.bgImages ?? [];
    int bgIndex = index - 1;
    if (bgIndex < 0 || bgIndex > mapModels.length - 1) {
      return SizedBox(height: _bouncingOffset);
    }

    var mapModel = mapModels[bgIndex];
    var bgFilePath = getResFilePath(mapModel.image ?? "");
    var imageWidget = Image.file(
      File(bgFilePath),
      fit: BoxFit.cover,
    );

    print('weich map bg $bgIndex');
    return SizedBox(
      width: mapFit(getMapWidth()),
      height: mapFit(mapModel.rect?.h ?? 0),
      child: imageWidget,
    );
  }

  /// 地图节点
  Widget getMapPointsWidget(BuildContext context, int index) {
    var groupHeights = widget.state.pointLayerGroupHeights ?? [];
    int layerIndex = index - 1;
    if (layerIndex < 0 || layerIndex > groupHeights.length - 1) {
      return SizedBox(height: _bouncingOffset);
    }

    var heights = widget.state.pointLayerGroupHeights ?? [];
    var pointCounts = widget.state.pointLayerGroupPointCounts ?? [];
    var points = widget.state.getPointsForLayer(layerIndex, pointCounts);

    var height = groupHeights[layerIndex];
    var width = getMapWidth();
    var startY = widget.state.getStartHeightForLayer(layerIndex, heights);
    var endY = startY + height;

    List<Widget> widgets = [];
    for (var i = 0; i < points.length; i++) {
      var point = points[i];
      var pointState = widget.state.getPointState(point);
      var pointWidget = getPointWidget(point, pointState, startY);
      widgets.add(pointWidget);

      if (pointState == widget.state.getCurrentPointState()) {
        var boatRect = widget.state.getPointBoatRect();
        if (boatRect != null) {
          widgets.add(getBoatWidget(boatRect, startY));
        }
      }
    }

    return Container(
      // color: Color((Random().nextDouble() * 0xFFFFFF).toInt() << 0).withOpacity(0.3),
      clipBehavior: Clip.none,
      width: mapFit(width),
      height: mapFit(height),
      child: Stack(
        clipBehavior: Clip.none,
        children: widgets,
      ),
    );
  }

  /// 获取当前背景图区域内的点（点的顶部在当前背景图区域内）
  List<TeamStudySeaMapPoint> getPagePoints(int bgIndex) {
    double startHeight = getBgStartHeight(bgIndex);

    var mapModel = widget.state.mapConfig?.bgImages?[bgIndex];
    double endHeight = startHeight + (mapModel?.rect?.h ?? 0);

    List<TeamStudySeaMapPoint> points = [];
    widget.state.mapConfig?.points?.forEach((point) {
      var pointY = point.rect?.y ?? 0;
      if (pointY >= startHeight && pointY < endHeight) {
        points.add(point);
      }
    });
    return points;
  }

  TeamStudySeaMapPointRes getMapResImage(String type) {
    var pointImages = widget.state.mapConfig?.pointImg ?? [];
    for (var pointImage in pointImages) {
      if (pointImage.type == type) {
        return pointImage;
      }
    }
    return pointImages[0];
  }

  String getResFilePath(String path) {
    return _controller.getResFilePath(path);
  }

  double getBgStartHeight(int bgIndex) {
    double startHeight = 0;
    mapBgHeight.forEach((key, value) {
      if (key < bgIndex) {
        startHeight += value;
      }
    });
    return startHeight;
  }

  double getMapWidth() {
    return widget.state.mapConfig?.bgWidth ?? 810;
  }

  double mapFit(double mapSize) {
    if (mapScale <= 0) {
      var mapWidth = getMapWidth();
      var viewWidth = MediaQuery.of(context).size.width;
      mapScale = viewWidth / mapWidth;
    }
    return mapScale * mapSize;
  }
}
