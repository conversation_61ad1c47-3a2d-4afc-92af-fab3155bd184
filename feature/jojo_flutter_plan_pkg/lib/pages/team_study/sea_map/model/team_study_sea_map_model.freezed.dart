// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'team_study_sea_map_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#custom-getters-and-methods');

TeamStudySeaMapModel _$TeamStudySeaMapModelFromJson(Map<String, dynamic> json) {
  return _TeamStudySeaMapModel.fromJson(json);
}

/// @nodoc
mixin _$TeamStudySeaMapModel {
  String? get title => throw _privateConstructorUsedError;
  String? get barColor => throw _privateConstructorUsedError;
  double? get bgWidth => throw _privateConstructorUsedError;
  double? get bgHeight => throw _privateConstructorUsedError;
  List<TeamStudySeaMapPointRes>? get bgImages =>
      throw _privateConstructorUsedError;
  List<TeamStudySeaMapPointRes>? get items =>
      throw _privateConstructorUsedError;
  List<TeamStudySeaMapPoint>? get points => throw _privateConstructorUsedError;
  List<TeamStudySeaMapPointRes>? get pointImg =>
      throw _privateConstructorUsedError;
  List<TeamStudySeaMapPointRes>? get pointSpine =>
      throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $TeamStudySeaMapModelCopyWith<TeamStudySeaMapModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $TeamStudySeaMapModelCopyWith<$Res> {
  factory $TeamStudySeaMapModelCopyWith(TeamStudySeaMapModel value,
          $Res Function(TeamStudySeaMapModel) then) =
      _$TeamStudySeaMapModelCopyWithImpl<$Res, TeamStudySeaMapModel>;
  @useResult
  $Res call(
      {String? title,
      String? barColor,
      double? bgWidth,
      double? bgHeight,
      List<TeamStudySeaMapPointRes>? bgImages,
      List<TeamStudySeaMapPointRes>? items,
      List<TeamStudySeaMapPoint>? points,
      List<TeamStudySeaMapPointRes>? pointImg,
      List<TeamStudySeaMapPointRes>? pointSpine});
}

/// @nodoc
class _$TeamStudySeaMapModelCopyWithImpl<$Res,
        $Val extends TeamStudySeaMapModel>
    implements $TeamStudySeaMapModelCopyWith<$Res> {
  _$TeamStudySeaMapModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? title = freezed,
    Object? barColor = freezed,
    Object? bgWidth = freezed,
    Object? bgHeight = freezed,
    Object? bgImages = freezed,
    Object? items = freezed,
    Object? points = freezed,
    Object? pointImg = freezed,
    Object? pointSpine = freezed,
  }) {
    return _then(_value.copyWith(
      title: freezed == title
          ? _value.title
          : title // ignore: cast_nullable_to_non_nullable
              as String?,
      barColor: freezed == barColor
          ? _value.barColor
          : barColor // ignore: cast_nullable_to_non_nullable
              as String?,
      bgWidth: freezed == bgWidth
          ? _value.bgWidth
          : bgWidth // ignore: cast_nullable_to_non_nullable
              as double?,
      bgHeight: freezed == bgHeight
          ? _value.bgHeight
          : bgHeight // ignore: cast_nullable_to_non_nullable
              as double?,
      bgImages: freezed == bgImages
          ? _value.bgImages
          : bgImages // ignore: cast_nullable_to_non_nullable
              as List<TeamStudySeaMapPointRes>?,
      items: freezed == items
          ? _value.items
          : items // ignore: cast_nullable_to_non_nullable
              as List<TeamStudySeaMapPointRes>?,
      points: freezed == points
          ? _value.points
          : points // ignore: cast_nullable_to_non_nullable
              as List<TeamStudySeaMapPoint>?,
      pointImg: freezed == pointImg
          ? _value.pointImg
          : pointImg // ignore: cast_nullable_to_non_nullable
              as List<TeamStudySeaMapPointRes>?,
      pointSpine: freezed == pointSpine
          ? _value.pointSpine
          : pointSpine // ignore: cast_nullable_to_non_nullable
              as List<TeamStudySeaMapPointRes>?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_TeamStudySeaMapModelCopyWith<$Res>
    implements $TeamStudySeaMapModelCopyWith<$Res> {
  factory _$$_TeamStudySeaMapModelCopyWith(_$_TeamStudySeaMapModel value,
          $Res Function(_$_TeamStudySeaMapModel) then) =
      __$$_TeamStudySeaMapModelCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String? title,
      String? barColor,
      double? bgWidth,
      double? bgHeight,
      List<TeamStudySeaMapPointRes>? bgImages,
      List<TeamStudySeaMapPointRes>? items,
      List<TeamStudySeaMapPoint>? points,
      List<TeamStudySeaMapPointRes>? pointImg,
      List<TeamStudySeaMapPointRes>? pointSpine});
}

/// @nodoc
class __$$_TeamStudySeaMapModelCopyWithImpl<$Res>
    extends _$TeamStudySeaMapModelCopyWithImpl<$Res, _$_TeamStudySeaMapModel>
    implements _$$_TeamStudySeaMapModelCopyWith<$Res> {
  __$$_TeamStudySeaMapModelCopyWithImpl(_$_TeamStudySeaMapModel _value,
      $Res Function(_$_TeamStudySeaMapModel) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? title = freezed,
    Object? barColor = freezed,
    Object? bgWidth = freezed,
    Object? bgHeight = freezed,
    Object? bgImages = freezed,
    Object? items = freezed,
    Object? points = freezed,
    Object? pointImg = freezed,
    Object? pointSpine = freezed,
  }) {
    return _then(_$_TeamStudySeaMapModel(
      title: freezed == title
          ? _value.title
          : title // ignore: cast_nullable_to_non_nullable
              as String?,
      barColor: freezed == barColor
          ? _value.barColor
          : barColor // ignore: cast_nullable_to_non_nullable
              as String?,
      bgWidth: freezed == bgWidth
          ? _value.bgWidth
          : bgWidth // ignore: cast_nullable_to_non_nullable
              as double?,
      bgHeight: freezed == bgHeight
          ? _value.bgHeight
          : bgHeight // ignore: cast_nullable_to_non_nullable
              as double?,
      bgImages: freezed == bgImages
          ? _value._bgImages
          : bgImages // ignore: cast_nullable_to_non_nullable
              as List<TeamStudySeaMapPointRes>?,
      items: freezed == items
          ? _value._items
          : items // ignore: cast_nullable_to_non_nullable
              as List<TeamStudySeaMapPointRes>?,
      points: freezed == points
          ? _value._points
          : points // ignore: cast_nullable_to_non_nullable
              as List<TeamStudySeaMapPoint>?,
      pointImg: freezed == pointImg
          ? _value._pointImg
          : pointImg // ignore: cast_nullable_to_non_nullable
              as List<TeamStudySeaMapPointRes>?,
      pointSpine: freezed == pointSpine
          ? _value._pointSpine
          : pointSpine // ignore: cast_nullable_to_non_nullable
              as List<TeamStudySeaMapPointRes>?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_TeamStudySeaMapModel implements _TeamStudySeaMapModel {
  _$_TeamStudySeaMapModel(
      {this.title,
      this.barColor,
      this.bgWidth,
      this.bgHeight,
      final List<TeamStudySeaMapPointRes>? bgImages,
      final List<TeamStudySeaMapPointRes>? items,
      final List<TeamStudySeaMapPoint>? points,
      final List<TeamStudySeaMapPointRes>? pointImg,
      final List<TeamStudySeaMapPointRes>? pointSpine})
      : _bgImages = bgImages,
        _items = items,
        _points = points,
        _pointImg = pointImg,
        _pointSpine = pointSpine;

  factory _$_TeamStudySeaMapModel.fromJson(Map<String, dynamic> json) =>
      _$$_TeamStudySeaMapModelFromJson(json);

  @override
  final String? title;
  @override
  final String? barColor;
  @override
  final double? bgWidth;
  @override
  final double? bgHeight;
  final List<TeamStudySeaMapPointRes>? _bgImages;
  @override
  List<TeamStudySeaMapPointRes>? get bgImages {
    final value = _bgImages;
    if (value == null) return null;
    if (_bgImages is EqualUnmodifiableListView) return _bgImages;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  final List<TeamStudySeaMapPointRes>? _items;
  @override
  List<TeamStudySeaMapPointRes>? get items {
    final value = _items;
    if (value == null) return null;
    if (_items is EqualUnmodifiableListView) return _items;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  final List<TeamStudySeaMapPoint>? _points;
  @override
  List<TeamStudySeaMapPoint>? get points {
    final value = _points;
    if (value == null) return null;
    if (_points is EqualUnmodifiableListView) return _points;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  final List<TeamStudySeaMapPointRes>? _pointImg;
  @override
  List<TeamStudySeaMapPointRes>? get pointImg {
    final value = _pointImg;
    if (value == null) return null;
    if (_pointImg is EqualUnmodifiableListView) return _pointImg;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  final List<TeamStudySeaMapPointRes>? _pointSpine;
  @override
  List<TeamStudySeaMapPointRes>? get pointSpine {
    final value = _pointSpine;
    if (value == null) return null;
    if (_pointSpine is EqualUnmodifiableListView) return _pointSpine;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(value);
  }

  @override
  String toString() {
    return 'TeamStudySeaMapModel(title: $title, barColor: $barColor, bgWidth: $bgWidth, bgHeight: $bgHeight, bgImages: $bgImages, items: $items, points: $points, pointImg: $pointImg, pointSpine: $pointSpine)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_TeamStudySeaMapModel &&
            (identical(other.title, title) || other.title == title) &&
            (identical(other.barColor, barColor) ||
                other.barColor == barColor) &&
            (identical(other.bgWidth, bgWidth) || other.bgWidth == bgWidth) &&
            (identical(other.bgHeight, bgHeight) ||
                other.bgHeight == bgHeight) &&
            const DeepCollectionEquality().equals(other._bgImages, _bgImages) &&
            const DeepCollectionEquality().equals(other._items, _items) &&
            const DeepCollectionEquality().equals(other._points, _points) &&
            const DeepCollectionEquality().equals(other._pointImg, _pointImg) &&
            const DeepCollectionEquality()
                .equals(other._pointSpine, _pointSpine));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      title,
      barColor,
      bgWidth,
      bgHeight,
      const DeepCollectionEquality().hash(_bgImages),
      const DeepCollectionEquality().hash(_items),
      const DeepCollectionEquality().hash(_points),
      const DeepCollectionEquality().hash(_pointImg),
      const DeepCollectionEquality().hash(_pointSpine));

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_TeamStudySeaMapModelCopyWith<_$_TeamStudySeaMapModel> get copyWith =>
      __$$_TeamStudySeaMapModelCopyWithImpl<_$_TeamStudySeaMapModel>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_TeamStudySeaMapModelToJson(
      this,
    );
  }
}

abstract class _TeamStudySeaMapModel implements TeamStudySeaMapModel {
  factory _TeamStudySeaMapModel(
          {final String? title,
          final String? barColor,
          final double? bgWidth,
          final double? bgHeight,
          final List<TeamStudySeaMapPointRes>? bgImages,
          final List<TeamStudySeaMapPointRes>? items,
          final List<TeamStudySeaMapPoint>? points,
          final List<TeamStudySeaMapPointRes>? pointImg,
          final List<TeamStudySeaMapPointRes>? pointSpine}) =
      _$_TeamStudySeaMapModel;

  factory _TeamStudySeaMapModel.fromJson(Map<String, dynamic> json) =
      _$_TeamStudySeaMapModel.fromJson;

  @override
  String? get title;
  @override
  String? get barColor;
  @override
  double? get bgWidth;
  @override
  double? get bgHeight;
  @override
  List<TeamStudySeaMapPointRes>? get bgImages;
  @override
  List<TeamStudySeaMapPointRes>? get items;
  @override
  List<TeamStudySeaMapPoint>? get points;
  @override
  List<TeamStudySeaMapPointRes>? get pointImg;
  @override
  List<TeamStudySeaMapPointRes>? get pointSpine;
  @override
  @JsonKey(ignore: true)
  _$$_TeamStudySeaMapModelCopyWith<_$_TeamStudySeaMapModel> get copyWith =>
      throw _privateConstructorUsedError;
}

TeamStudySeaMapPoint _$TeamStudySeaMapPointFromJson(Map<String, dynamic> json) {
  return _TeamStudySeaMapPoint.fromJson(json);
}

/// @nodoc
mixin _$TeamStudySeaMapPoint {
  int? get id => throw _privateConstructorUsedError;
  TeamStudySeaMapRect? get rect => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $TeamStudySeaMapPointCopyWith<TeamStudySeaMapPoint> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $TeamStudySeaMapPointCopyWith<$Res> {
  factory $TeamStudySeaMapPointCopyWith(TeamStudySeaMapPoint value,
          $Res Function(TeamStudySeaMapPoint) then) =
      _$TeamStudySeaMapPointCopyWithImpl<$Res, TeamStudySeaMapPoint>;
  @useResult
  $Res call({int? id, TeamStudySeaMapRect? rect});

  $TeamStudySeaMapRectCopyWith<$Res>? get rect;
}

/// @nodoc
class _$TeamStudySeaMapPointCopyWithImpl<$Res,
        $Val extends TeamStudySeaMapPoint>
    implements $TeamStudySeaMapPointCopyWith<$Res> {
  _$TeamStudySeaMapPointCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = freezed,
    Object? rect = freezed,
  }) {
    return _then(_value.copyWith(
      id: freezed == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as int?,
      rect: freezed == rect
          ? _value.rect
          : rect // ignore: cast_nullable_to_non_nullable
              as TeamStudySeaMapRect?,
    ) as $Val);
  }

  @override
  @pragma('vm:prefer-inline')
  $TeamStudySeaMapRectCopyWith<$Res>? get rect {
    if (_value.rect == null) {
      return null;
    }

    return $TeamStudySeaMapRectCopyWith<$Res>(_value.rect!, (value) {
      return _then(_value.copyWith(rect: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$_TeamStudySeaMapPointCopyWith<$Res>
    implements $TeamStudySeaMapPointCopyWith<$Res> {
  factory _$$_TeamStudySeaMapPointCopyWith(_$_TeamStudySeaMapPoint value,
          $Res Function(_$_TeamStudySeaMapPoint) then) =
      __$$_TeamStudySeaMapPointCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({int? id, TeamStudySeaMapRect? rect});

  @override
  $TeamStudySeaMapRectCopyWith<$Res>? get rect;
}

/// @nodoc
class __$$_TeamStudySeaMapPointCopyWithImpl<$Res>
    extends _$TeamStudySeaMapPointCopyWithImpl<$Res, _$_TeamStudySeaMapPoint>
    implements _$$_TeamStudySeaMapPointCopyWith<$Res> {
  __$$_TeamStudySeaMapPointCopyWithImpl(_$_TeamStudySeaMapPoint _value,
      $Res Function(_$_TeamStudySeaMapPoint) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = freezed,
    Object? rect = freezed,
  }) {
    return _then(_$_TeamStudySeaMapPoint(
      id: freezed == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as int?,
      rect: freezed == rect
          ? _value.rect
          : rect // ignore: cast_nullable_to_non_nullable
              as TeamStudySeaMapRect?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_TeamStudySeaMapPoint implements _TeamStudySeaMapPoint {
  _$_TeamStudySeaMapPoint({this.id, this.rect});

  factory _$_TeamStudySeaMapPoint.fromJson(Map<String, dynamic> json) =>
      _$$_TeamStudySeaMapPointFromJson(json);

  @override
  final int? id;
  @override
  final TeamStudySeaMapRect? rect;

  @override
  String toString() {
    return 'TeamStudySeaMapPoint(id: $id, rect: $rect)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_TeamStudySeaMapPoint &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.rect, rect) || other.rect == rect));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, id, rect);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_TeamStudySeaMapPointCopyWith<_$_TeamStudySeaMapPoint> get copyWith =>
      __$$_TeamStudySeaMapPointCopyWithImpl<_$_TeamStudySeaMapPoint>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_TeamStudySeaMapPointToJson(
      this,
    );
  }
}

abstract class _TeamStudySeaMapPoint implements TeamStudySeaMapPoint {
  factory _TeamStudySeaMapPoint(
      {final int? id,
      final TeamStudySeaMapRect? rect}) = _$_TeamStudySeaMapPoint;

  factory _TeamStudySeaMapPoint.fromJson(Map<String, dynamic> json) =
      _$_TeamStudySeaMapPoint.fromJson;

  @override
  int? get id;
  @override
  TeamStudySeaMapRect? get rect;
  @override
  @JsonKey(ignore: true)
  _$$_TeamStudySeaMapPointCopyWith<_$_TeamStudySeaMapPoint> get copyWith =>
      throw _privateConstructorUsedError;
}

TeamStudySeaMapPointRes _$TeamStudySeaMapPointResFromJson(
    Map<String, dynamic> json) {
  return _TeamStudySeaMapPointRes.fromJson(json);
}

/// @nodoc
mixin _$TeamStudySeaMapPointRes {
  String? get type => throw _privateConstructorUsedError;
  String? get image => throw _privateConstructorUsedError;
  String? get skeletonFile => throw _privateConstructorUsedError;
  String? get atlasFile => throw _privateConstructorUsedError;
  String? get animationName => throw _privateConstructorUsedError;
  TeamStudySeaMapRect? get rect => throw _privateConstructorUsedError;
  TeamStudySeaMapResType? get resType => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $TeamStudySeaMapPointResCopyWith<TeamStudySeaMapPointRes> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $TeamStudySeaMapPointResCopyWith<$Res> {
  factory $TeamStudySeaMapPointResCopyWith(TeamStudySeaMapPointRes value,
          $Res Function(TeamStudySeaMapPointRes) then) =
      _$TeamStudySeaMapPointResCopyWithImpl<$Res, TeamStudySeaMapPointRes>;
  @useResult
  $Res call(
      {String? type,
      String? image,
      String? skeletonFile,
      String? atlasFile,
      String? animationName,
      TeamStudySeaMapRect? rect,
      TeamStudySeaMapResType? resType});

  $TeamStudySeaMapRectCopyWith<$Res>? get rect;
}

/// @nodoc
class _$TeamStudySeaMapPointResCopyWithImpl<$Res,
        $Val extends TeamStudySeaMapPointRes>
    implements $TeamStudySeaMapPointResCopyWith<$Res> {
  _$TeamStudySeaMapPointResCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? type = freezed,
    Object? image = freezed,
    Object? skeletonFile = freezed,
    Object? atlasFile = freezed,
    Object? animationName = freezed,
    Object? rect = freezed,
    Object? resType = freezed,
  }) {
    return _then(_value.copyWith(
      type: freezed == type
          ? _value.type
          : type // ignore: cast_nullable_to_non_nullable
              as String?,
      image: freezed == image
          ? _value.image
          : image // ignore: cast_nullable_to_non_nullable
              as String?,
      skeletonFile: freezed == skeletonFile
          ? _value.skeletonFile
          : skeletonFile // ignore: cast_nullable_to_non_nullable
              as String?,
      atlasFile: freezed == atlasFile
          ? _value.atlasFile
          : atlasFile // ignore: cast_nullable_to_non_nullable
              as String?,
      animationName: freezed == animationName
          ? _value.animationName
          : animationName // ignore: cast_nullable_to_non_nullable
              as String?,
      rect: freezed == rect
          ? _value.rect
          : rect // ignore: cast_nullable_to_non_nullable
              as TeamStudySeaMapRect?,
      resType: freezed == resType
          ? _value.resType
          : resType // ignore: cast_nullable_to_non_nullable
              as TeamStudySeaMapResType?,
    ) as $Val);
  }

  @override
  @pragma('vm:prefer-inline')
  $TeamStudySeaMapRectCopyWith<$Res>? get rect {
    if (_value.rect == null) {
      return null;
    }

    return $TeamStudySeaMapRectCopyWith<$Res>(_value.rect!, (value) {
      return _then(_value.copyWith(rect: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$_TeamStudySeaMapPointResCopyWith<$Res>
    implements $TeamStudySeaMapPointResCopyWith<$Res> {
  factory _$$_TeamStudySeaMapPointResCopyWith(_$_TeamStudySeaMapPointRes value,
          $Res Function(_$_TeamStudySeaMapPointRes) then) =
      __$$_TeamStudySeaMapPointResCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String? type,
      String? image,
      String? skeletonFile,
      String? atlasFile,
      String? animationName,
      TeamStudySeaMapRect? rect,
      TeamStudySeaMapResType? resType});

  @override
  $TeamStudySeaMapRectCopyWith<$Res>? get rect;
}

/// @nodoc
class __$$_TeamStudySeaMapPointResCopyWithImpl<$Res>
    extends _$TeamStudySeaMapPointResCopyWithImpl<$Res,
        _$_TeamStudySeaMapPointRes>
    implements _$$_TeamStudySeaMapPointResCopyWith<$Res> {
  __$$_TeamStudySeaMapPointResCopyWithImpl(_$_TeamStudySeaMapPointRes _value,
      $Res Function(_$_TeamStudySeaMapPointRes) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? type = freezed,
    Object? image = freezed,
    Object? skeletonFile = freezed,
    Object? atlasFile = freezed,
    Object? animationName = freezed,
    Object? rect = freezed,
    Object? resType = freezed,
  }) {
    return _then(_$_TeamStudySeaMapPointRes(
      type: freezed == type
          ? _value.type
          : type // ignore: cast_nullable_to_non_nullable
              as String?,
      image: freezed == image
          ? _value.image
          : image // ignore: cast_nullable_to_non_nullable
              as String?,
      skeletonFile: freezed == skeletonFile
          ? _value.skeletonFile
          : skeletonFile // ignore: cast_nullable_to_non_nullable
              as String?,
      atlasFile: freezed == atlasFile
          ? _value.atlasFile
          : atlasFile // ignore: cast_nullable_to_non_nullable
              as String?,
      animationName: freezed == animationName
          ? _value.animationName
          : animationName // ignore: cast_nullable_to_non_nullable
              as String?,
      rect: freezed == rect
          ? _value.rect
          : rect // ignore: cast_nullable_to_non_nullable
              as TeamStudySeaMapRect?,
      resType: freezed == resType
          ? _value.resType
          : resType // ignore: cast_nullable_to_non_nullable
              as TeamStudySeaMapResType?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_TeamStudySeaMapPointRes implements _TeamStudySeaMapPointRes {
  _$_TeamStudySeaMapPointRes(
      {this.type,
      this.image,
      this.skeletonFile,
      this.atlasFile,
      this.animationName,
      this.rect,
      this.resType});

  factory _$_TeamStudySeaMapPointRes.fromJson(Map<String, dynamic> json) =>
      _$$_TeamStudySeaMapPointResFromJson(json);

  @override
  final String? type;
  @override
  final String? image;
  @override
  final String? skeletonFile;
  @override
  final String? atlasFile;
  @override
  final String? animationName;
  @override
  final TeamStudySeaMapRect? rect;
  @override
  final TeamStudySeaMapResType? resType;

  @override
  String toString() {
    return 'TeamStudySeaMapPointRes(type: $type, image: $image, skeletonFile: $skeletonFile, atlasFile: $atlasFile, animationName: $animationName, rect: $rect, resType: $resType)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_TeamStudySeaMapPointRes &&
            (identical(other.type, type) || other.type == type) &&
            (identical(other.image, image) || other.image == image) &&
            (identical(other.skeletonFile, skeletonFile) ||
                other.skeletonFile == skeletonFile) &&
            (identical(other.atlasFile, atlasFile) ||
                other.atlasFile == atlasFile) &&
            (identical(other.animationName, animationName) ||
                other.animationName == animationName) &&
            (identical(other.rect, rect) || other.rect == rect) &&
            (identical(other.resType, resType) || other.resType == resType));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, type, image, skeletonFile,
      atlasFile, animationName, rect, resType);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_TeamStudySeaMapPointResCopyWith<_$_TeamStudySeaMapPointRes>
      get copyWith =>
          __$$_TeamStudySeaMapPointResCopyWithImpl<_$_TeamStudySeaMapPointRes>(
              this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_TeamStudySeaMapPointResToJson(
      this,
    );
  }
}

abstract class _TeamStudySeaMapPointRes implements TeamStudySeaMapPointRes {
  factory _TeamStudySeaMapPointRes(
      {final String? type,
      final String? image,
      final String? skeletonFile,
      final String? atlasFile,
      final String? animationName,
      final TeamStudySeaMapRect? rect,
      final TeamStudySeaMapResType? resType}) = _$_TeamStudySeaMapPointRes;

  factory _TeamStudySeaMapPointRes.fromJson(Map<String, dynamic> json) =
      _$_TeamStudySeaMapPointRes.fromJson;

  @override
  String? get type;
  @override
  String? get image;
  @override
  String? get skeletonFile;
  @override
  String? get atlasFile;
  @override
  String? get animationName;
  @override
  TeamStudySeaMapRect? get rect;
  @override
  TeamStudySeaMapResType? get resType;
  @override
  @JsonKey(ignore: true)
  _$$_TeamStudySeaMapPointResCopyWith<_$_TeamStudySeaMapPointRes>
      get copyWith => throw _privateConstructorUsedError;
}

TeamStudySeaMapRect _$TeamStudySeaMapRectFromJson(Map<String, dynamic> json) {
  return _TeamStudySeaMapRect.fromJson(json);
}

/// @nodoc
mixin _$TeamStudySeaMapRect {
  double? get x => throw _privateConstructorUsedError;
  double? get y => throw _privateConstructorUsedError;
  double? get w => throw _privateConstructorUsedError;
  double? get h => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $TeamStudySeaMapRectCopyWith<TeamStudySeaMapRect> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $TeamStudySeaMapRectCopyWith<$Res> {
  factory $TeamStudySeaMapRectCopyWith(
          TeamStudySeaMapRect value, $Res Function(TeamStudySeaMapRect) then) =
      _$TeamStudySeaMapRectCopyWithImpl<$Res, TeamStudySeaMapRect>;
  @useResult
  $Res call({double? x, double? y, double? w, double? h});
}

/// @nodoc
class _$TeamStudySeaMapRectCopyWithImpl<$Res, $Val extends TeamStudySeaMapRect>
    implements $TeamStudySeaMapRectCopyWith<$Res> {
  _$TeamStudySeaMapRectCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? x = freezed,
    Object? y = freezed,
    Object? w = freezed,
    Object? h = freezed,
  }) {
    return _then(_value.copyWith(
      x: freezed == x
          ? _value.x
          : x // ignore: cast_nullable_to_non_nullable
              as double?,
      y: freezed == y
          ? _value.y
          : y // ignore: cast_nullable_to_non_nullable
              as double?,
      w: freezed == w
          ? _value.w
          : w // ignore: cast_nullable_to_non_nullable
              as double?,
      h: freezed == h
          ? _value.h
          : h // ignore: cast_nullable_to_non_nullable
              as double?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_TeamStudySeaMapRectCopyWith<$Res>
    implements $TeamStudySeaMapRectCopyWith<$Res> {
  factory _$$_TeamStudySeaMapRectCopyWith(_$_TeamStudySeaMapRect value,
          $Res Function(_$_TeamStudySeaMapRect) then) =
      __$$_TeamStudySeaMapRectCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({double? x, double? y, double? w, double? h});
}

/// @nodoc
class __$$_TeamStudySeaMapRectCopyWithImpl<$Res>
    extends _$TeamStudySeaMapRectCopyWithImpl<$Res, _$_TeamStudySeaMapRect>
    implements _$$_TeamStudySeaMapRectCopyWith<$Res> {
  __$$_TeamStudySeaMapRectCopyWithImpl(_$_TeamStudySeaMapRect _value,
      $Res Function(_$_TeamStudySeaMapRect) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? x = freezed,
    Object? y = freezed,
    Object? w = freezed,
    Object? h = freezed,
  }) {
    return _then(_$_TeamStudySeaMapRect(
      x: freezed == x
          ? _value.x
          : x // ignore: cast_nullable_to_non_nullable
              as double?,
      y: freezed == y
          ? _value.y
          : y // ignore: cast_nullable_to_non_nullable
              as double?,
      w: freezed == w
          ? _value.w
          : w // ignore: cast_nullable_to_non_nullable
              as double?,
      h: freezed == h
          ? _value.h
          : h // ignore: cast_nullable_to_non_nullable
              as double?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_TeamStudySeaMapRect implements _TeamStudySeaMapRect {
  _$_TeamStudySeaMapRect({this.x, this.y, this.w, this.h});

  factory _$_TeamStudySeaMapRect.fromJson(Map<String, dynamic> json) =>
      _$$_TeamStudySeaMapRectFromJson(json);

  @override
  final double? x;
  @override
  final double? y;
  @override
  final double? w;
  @override
  final double? h;

  @override
  String toString() {
    return 'TeamStudySeaMapRect(x: $x, y: $y, w: $w, h: $h)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_TeamStudySeaMapRect &&
            (identical(other.x, x) || other.x == x) &&
            (identical(other.y, y) || other.y == y) &&
            (identical(other.w, w) || other.w == w) &&
            (identical(other.h, h) || other.h == h));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, x, y, w, h);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_TeamStudySeaMapRectCopyWith<_$_TeamStudySeaMapRect> get copyWith =>
      __$$_TeamStudySeaMapRectCopyWithImpl<_$_TeamStudySeaMapRect>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_TeamStudySeaMapRectToJson(
      this,
    );
  }
}

abstract class _TeamStudySeaMapRect implements TeamStudySeaMapRect {
  factory _TeamStudySeaMapRect(
      {final double? x,
      final double? y,
      final double? w,
      final double? h}) = _$_TeamStudySeaMapRect;

  factory _TeamStudySeaMapRect.fromJson(Map<String, dynamic> json) =
      _$_TeamStudySeaMapRect.fromJson;

  @override
  double? get x;
  @override
  double? get y;
  @override
  double? get w;
  @override
  double? get h;
  @override
  @JsonKey(ignore: true)
  _$$_TeamStudySeaMapRectCopyWith<_$_TeamStudySeaMapRect> get copyWith =>
      throw _privateConstructorUsedError;
}

TeamStudySeaMapPointState _$TeamStudySeaMapPointStateFromJson(
    Map<String, dynamic> json) {
  return _TeamStudySeaMapPointState.fromJson(json);
}

/// @nodoc
mixin _$TeamStudySeaMapPointState {
  int? get teamId => throw _privateConstructorUsedError;
  set teamId(int? value) => throw _privateConstructorUsedError;
  int? get nodeId => throw _privateConstructorUsedError;
  set nodeId(int? value) => throw _privateConstructorUsedError;
  int? get number => throw _privateConstructorUsedError;
  set number(int? value) => throw _privateConstructorUsedError;
  int? get isFinish => throw _privateConstructorUsedError;
  set isFinish(int? value) => throw _privateConstructorUsedError;
  int? get isReceiveReward => throw _privateConstructorUsedError;
  set isReceiveReward(int? value) => throw _privateConstructorUsedError;
  int? get receiveTime => throw _privateConstructorUsedError;
  set receiveTime(int? value) => throw _privateConstructorUsedError; // 节点奖励类型
  TeamStudySeaMapRewardType? get nodeRewardType =>
      throw _privateConstructorUsedError; // 节点奖励类型
  set nodeRewardType(TeamStudySeaMapRewardType? value) =>
      throw _privateConstructorUsedError;
  List<TeamStudySeaMapPointReward>? get rewardList =>
      throw _privateConstructorUsedError;
  set rewardList(List<TeamStudySeaMapPointReward>? value) =>
      throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $TeamStudySeaMapPointStateCopyWith<TeamStudySeaMapPointState> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $TeamStudySeaMapPointStateCopyWith<$Res> {
  factory $TeamStudySeaMapPointStateCopyWith(TeamStudySeaMapPointState value,
          $Res Function(TeamStudySeaMapPointState) then) =
      _$TeamStudySeaMapPointStateCopyWithImpl<$Res, TeamStudySeaMapPointState>;
  @useResult
  $Res call(
      {int? teamId,
      int? nodeId,
      int? number,
      int? isFinish,
      int? isReceiveReward,
      int? receiveTime,
      TeamStudySeaMapRewardType? nodeRewardType,
      List<TeamStudySeaMapPointReward>? rewardList});
}

/// @nodoc
class _$TeamStudySeaMapPointStateCopyWithImpl<$Res,
        $Val extends TeamStudySeaMapPointState>
    implements $TeamStudySeaMapPointStateCopyWith<$Res> {
  _$TeamStudySeaMapPointStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? teamId = freezed,
    Object? nodeId = freezed,
    Object? number = freezed,
    Object? isFinish = freezed,
    Object? isReceiveReward = freezed,
    Object? receiveTime = freezed,
    Object? nodeRewardType = freezed,
    Object? rewardList = freezed,
  }) {
    return _then(_value.copyWith(
      teamId: freezed == teamId
          ? _value.teamId
          : teamId // ignore: cast_nullable_to_non_nullable
              as int?,
      nodeId: freezed == nodeId
          ? _value.nodeId
          : nodeId // ignore: cast_nullable_to_non_nullable
              as int?,
      number: freezed == number
          ? _value.number
          : number // ignore: cast_nullable_to_non_nullable
              as int?,
      isFinish: freezed == isFinish
          ? _value.isFinish
          : isFinish // ignore: cast_nullable_to_non_nullable
              as int?,
      isReceiveReward: freezed == isReceiveReward
          ? _value.isReceiveReward
          : isReceiveReward // ignore: cast_nullable_to_non_nullable
              as int?,
      receiveTime: freezed == receiveTime
          ? _value.receiveTime
          : receiveTime // ignore: cast_nullable_to_non_nullable
              as int?,
      nodeRewardType: freezed == nodeRewardType
          ? _value.nodeRewardType
          : nodeRewardType // ignore: cast_nullable_to_non_nullable
              as TeamStudySeaMapRewardType?,
      rewardList: freezed == rewardList
          ? _value.rewardList
          : rewardList // ignore: cast_nullable_to_non_nullable
              as List<TeamStudySeaMapPointReward>?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_TeamStudySeaMapPointStateCopyWith<$Res>
    implements $TeamStudySeaMapPointStateCopyWith<$Res> {
  factory _$$_TeamStudySeaMapPointStateCopyWith(
          _$_TeamStudySeaMapPointState value,
          $Res Function(_$_TeamStudySeaMapPointState) then) =
      __$$_TeamStudySeaMapPointStateCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {int? teamId,
      int? nodeId,
      int? number,
      int? isFinish,
      int? isReceiveReward,
      int? receiveTime,
      TeamStudySeaMapRewardType? nodeRewardType,
      List<TeamStudySeaMapPointReward>? rewardList});
}

/// @nodoc
class __$$_TeamStudySeaMapPointStateCopyWithImpl<$Res>
    extends _$TeamStudySeaMapPointStateCopyWithImpl<$Res,
        _$_TeamStudySeaMapPointState>
    implements _$$_TeamStudySeaMapPointStateCopyWith<$Res> {
  __$$_TeamStudySeaMapPointStateCopyWithImpl(
      _$_TeamStudySeaMapPointState _value,
      $Res Function(_$_TeamStudySeaMapPointState) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? teamId = freezed,
    Object? nodeId = freezed,
    Object? number = freezed,
    Object? isFinish = freezed,
    Object? isReceiveReward = freezed,
    Object? receiveTime = freezed,
    Object? nodeRewardType = freezed,
    Object? rewardList = freezed,
  }) {
    return _then(_$_TeamStudySeaMapPointState(
      teamId: freezed == teamId
          ? _value.teamId
          : teamId // ignore: cast_nullable_to_non_nullable
              as int?,
      nodeId: freezed == nodeId
          ? _value.nodeId
          : nodeId // ignore: cast_nullable_to_non_nullable
              as int?,
      number: freezed == number
          ? _value.number
          : number // ignore: cast_nullable_to_non_nullable
              as int?,
      isFinish: freezed == isFinish
          ? _value.isFinish
          : isFinish // ignore: cast_nullable_to_non_nullable
              as int?,
      isReceiveReward: freezed == isReceiveReward
          ? _value.isReceiveReward
          : isReceiveReward // ignore: cast_nullable_to_non_nullable
              as int?,
      receiveTime: freezed == receiveTime
          ? _value.receiveTime
          : receiveTime // ignore: cast_nullable_to_non_nullable
              as int?,
      nodeRewardType: freezed == nodeRewardType
          ? _value.nodeRewardType
          : nodeRewardType // ignore: cast_nullable_to_non_nullable
              as TeamStudySeaMapRewardType?,
      rewardList: freezed == rewardList
          ? _value.rewardList
          : rewardList // ignore: cast_nullable_to_non_nullable
              as List<TeamStudySeaMapPointReward>?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_TeamStudySeaMapPointState implements _TeamStudySeaMapPointState {
  _$_TeamStudySeaMapPointState(
      {this.teamId,
      this.nodeId,
      this.number,
      this.isFinish,
      this.isReceiveReward,
      this.receiveTime,
      this.nodeRewardType,
      this.rewardList});

  factory _$_TeamStudySeaMapPointState.fromJson(Map<String, dynamic> json) =>
      _$$_TeamStudySeaMapPointStateFromJson(json);

  @override
  int? teamId;
  @override
  int? nodeId;
  @override
  int? number;
  @override
  int? isFinish;
  @override
  int? isReceiveReward;
  @override
  int? receiveTime;
// 节点奖励类型
  @override
  TeamStudySeaMapRewardType? nodeRewardType;
  @override
  List<TeamStudySeaMapPointReward>? rewardList;

  @override
  String toString() {
    return 'TeamStudySeaMapPointState(teamId: $teamId, nodeId: $nodeId, number: $number, isFinish: $isFinish, isReceiveReward: $isReceiveReward, receiveTime: $receiveTime, nodeRewardType: $nodeRewardType, rewardList: $rewardList)';
  }

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_TeamStudySeaMapPointStateCopyWith<_$_TeamStudySeaMapPointState>
      get copyWith => __$$_TeamStudySeaMapPointStateCopyWithImpl<
          _$_TeamStudySeaMapPointState>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_TeamStudySeaMapPointStateToJson(
      this,
    );
  }
}

abstract class _TeamStudySeaMapPointState implements TeamStudySeaMapPointState {
  factory _TeamStudySeaMapPointState(
          {int? teamId,
          int? nodeId,
          int? number,
          int? isFinish,
          int? isReceiveReward,
          int? receiveTime,
          TeamStudySeaMapRewardType? nodeRewardType,
          List<TeamStudySeaMapPointReward>? rewardList}) =
      _$_TeamStudySeaMapPointState;

  factory _TeamStudySeaMapPointState.fromJson(Map<String, dynamic> json) =
      _$_TeamStudySeaMapPointState.fromJson;

  @override
  int? get teamId;
  set teamId(int? value);
  @override
  int? get nodeId;
  set nodeId(int? value);
  @override
  int? get number;
  set number(int? value);
  @override
  int? get isFinish;
  set isFinish(int? value);
  @override
  int? get isReceiveReward;
  set isReceiveReward(int? value);
  @override
  int? get receiveTime;
  set receiveTime(int? value);
  @override // 节点奖励类型
  TeamStudySeaMapRewardType? get nodeRewardType; // 节点奖励类型
  set nodeRewardType(TeamStudySeaMapRewardType? value);
  @override
  List<TeamStudySeaMapPointReward>? get rewardList;
  set rewardList(List<TeamStudySeaMapPointReward>? value);
  @override
  @JsonKey(ignore: true)
  _$$_TeamStudySeaMapPointStateCopyWith<_$_TeamStudySeaMapPointState>
      get copyWith => throw _privateConstructorUsedError;
}

TeamStudySeaMapPointReward _$TeamStudySeaMapPointRewardFromJson(
    Map<String, dynamic> json) {
  return _TeamStudySeaMapPointReward.fromJson(json);
}

/// @nodoc
mixin _$TeamStudySeaMapPointReward {
  int? get id => throw _privateConstructorUsedError; // 宝箱奖励类型(1道具)
  int? get type => throw _privateConstructorUsedError;
  int? get num => throw _privateConstructorUsedError;
  String? get name => throw _privateConstructorUsedError;
  String? get desc => throw _privateConstructorUsedError;
  String? get img => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $TeamStudySeaMapPointRewardCopyWith<TeamStudySeaMapPointReward>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $TeamStudySeaMapPointRewardCopyWith<$Res> {
  factory $TeamStudySeaMapPointRewardCopyWith(TeamStudySeaMapPointReward value,
          $Res Function(TeamStudySeaMapPointReward) then) =
      _$TeamStudySeaMapPointRewardCopyWithImpl<$Res,
          TeamStudySeaMapPointReward>;
  @useResult
  $Res call(
      {int? id, int? type, int? num, String? name, String? desc, String? img});
}

/// @nodoc
class _$TeamStudySeaMapPointRewardCopyWithImpl<$Res,
        $Val extends TeamStudySeaMapPointReward>
    implements $TeamStudySeaMapPointRewardCopyWith<$Res> {
  _$TeamStudySeaMapPointRewardCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = freezed,
    Object? type = freezed,
    Object? num = freezed,
    Object? name = freezed,
    Object? desc = freezed,
    Object? img = freezed,
  }) {
    return _then(_value.copyWith(
      id: freezed == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as int?,
      type: freezed == type
          ? _value.type
          : type // ignore: cast_nullable_to_non_nullable
              as int?,
      num: freezed == num
          ? _value.num
          : num // ignore: cast_nullable_to_non_nullable
              as int?,
      name: freezed == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String?,
      desc: freezed == desc
          ? _value.desc
          : desc // ignore: cast_nullable_to_non_nullable
              as String?,
      img: freezed == img
          ? _value.img
          : img // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$_TeamStudySeaMapPointRewardCopyWith<$Res>
    implements $TeamStudySeaMapPointRewardCopyWith<$Res> {
  factory _$$_TeamStudySeaMapPointRewardCopyWith(
          _$_TeamStudySeaMapPointReward value,
          $Res Function(_$_TeamStudySeaMapPointReward) then) =
      __$$_TeamStudySeaMapPointRewardCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {int? id, int? type, int? num, String? name, String? desc, String? img});
}

/// @nodoc
class __$$_TeamStudySeaMapPointRewardCopyWithImpl<$Res>
    extends _$TeamStudySeaMapPointRewardCopyWithImpl<$Res,
        _$_TeamStudySeaMapPointReward>
    implements _$$_TeamStudySeaMapPointRewardCopyWith<$Res> {
  __$$_TeamStudySeaMapPointRewardCopyWithImpl(
      _$_TeamStudySeaMapPointReward _value,
      $Res Function(_$_TeamStudySeaMapPointReward) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = freezed,
    Object? type = freezed,
    Object? num = freezed,
    Object? name = freezed,
    Object? desc = freezed,
    Object? img = freezed,
  }) {
    return _then(_$_TeamStudySeaMapPointReward(
      id: freezed == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as int?,
      type: freezed == type
          ? _value.type
          : type // ignore: cast_nullable_to_non_nullable
              as int?,
      num: freezed == num
          ? _value.num
          : num // ignore: cast_nullable_to_non_nullable
              as int?,
      name: freezed == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String?,
      desc: freezed == desc
          ? _value.desc
          : desc // ignore: cast_nullable_to_non_nullable
              as String?,
      img: freezed == img
          ? _value.img
          : img // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$_TeamStudySeaMapPointReward implements _TeamStudySeaMapPointReward {
  _$_TeamStudySeaMapPointReward(
      {this.id, this.type, this.num, this.name, this.desc, this.img});

  factory _$_TeamStudySeaMapPointReward.fromJson(Map<String, dynamic> json) =>
      _$$_TeamStudySeaMapPointRewardFromJson(json);

  @override
  final int? id;
// 宝箱奖励类型(1道具)
  @override
  final int? type;
  @override
  final int? num;
  @override
  final String? name;
  @override
  final String? desc;
  @override
  final String? img;

  @override
  String toString() {
    return 'TeamStudySeaMapPointReward(id: $id, type: $type, num: $num, name: $name, desc: $desc, img: $img)';
  }

  @override
  bool operator ==(dynamic other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$_TeamStudySeaMapPointReward &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.type, type) || other.type == type) &&
            (identical(other.num, num) || other.num == num) &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.desc, desc) || other.desc == desc) &&
            (identical(other.img, img) || other.img == img));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, id, type, num, name, desc, img);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$_TeamStudySeaMapPointRewardCopyWith<_$_TeamStudySeaMapPointReward>
      get copyWith => __$$_TeamStudySeaMapPointRewardCopyWithImpl<
          _$_TeamStudySeaMapPointReward>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$_TeamStudySeaMapPointRewardToJson(
      this,
    );
  }
}

abstract class _TeamStudySeaMapPointReward
    implements TeamStudySeaMapPointReward {
  factory _TeamStudySeaMapPointReward(
      {final int? id,
      final int? type,
      final int? num,
      final String? name,
      final String? desc,
      final String? img}) = _$_TeamStudySeaMapPointReward;

  factory _TeamStudySeaMapPointReward.fromJson(Map<String, dynamic> json) =
      _$_TeamStudySeaMapPointReward.fromJson;

  @override
  int? get id;
  @override // 宝箱奖励类型(1道具)
  int? get type;
  @override
  int? get num;
  @override
  String? get name;
  @override
  String? get desc;
  @override
  String? get img;
  @override
  @JsonKey(ignore: true)
  _$$_TeamStudySeaMapPointRewardCopyWith<_$_TeamStudySeaMapPointReward>
      get copyWith => throw _privateConstructorUsedError;
}
