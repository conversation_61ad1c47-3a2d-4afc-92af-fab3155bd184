import 'package:freezed_annotation/freezed_annotation.dart';

import '../state.dart';

part 'team_study_sea_map_model.freezed.dart';
part 'team_study_sea_map_model.g.dart';

enum TeamStudySeaMapResType {
  image,
  spine,
}

enum TeamStudySeaMapRewardType {
  @JsonValue(0)
  none, // 无奖励
  @JsonValue(1)
  reward, // 普通宝箱
  @JsonValue(2)
  photo, // 拍照
  @JsonValue(3)
  rewardHigh, // 高级宝箱
}

@freezed
class TeamStudySeaMapModel with _$TeamStudySeaMapModel {
  factory TeamStudySeaMapModel({
    String? title,
    String? barColor,
    double? bgWidth,
    double? bgHeight,
    List<TeamStudySeaMapPointRes>? bgImages,
    List<TeamStudySeaMapPointRes>? items,
    List<TeamStudySeaMapPoint>? points,
    List<TeamStudySeaMapPointRes>? pointImg,
    List<TeamStudySeaMapPointRes>? pointSpine,
  }) = _TeamStudySeaMapModel;

  factory TeamStudySeaMapModel.fromJson(Map<String, dynamic> json) =>
      _$TeamStudySeaMapModelFromJson(json);
}

@freezed
class TeamStudySeaMapPoint with _$TeamStudySeaMapPoint {
  factory TeamStudySeaMapPoint({
    int? id,
    TeamStudySeaMapRect? rect,
  }) = _TeamStudySeaMapPoint;

  factory TeamStudySeaMapPoint.fromJson(Map<String, dynamic> json) =>
      _$TeamStudySeaMapPointFromJson(json);
}

@freezed
class TeamStudySeaMapPointRes with _$TeamStudySeaMapPointRes {
  factory TeamStudySeaMapPointRes({
    String? type,
    String? image,
    String? skeletonFile,
    String? atlasFile,
    String? animationName,
    TeamStudySeaMapRect? rect,
    TeamStudySeaMapResType? resType,
  }) = _TeamStudySeaMapPointRes;

  factory TeamStudySeaMapPointRes.fromJson(Map<String, dynamic> json) =>
      _$TeamStudySeaMapPointResFromJson(json);
}

@freezed
class TeamStudySeaMapRect with _$TeamStudySeaMapRect {
  factory TeamStudySeaMapRect({
    double? x,
    double? y,
    double? w,
    double? h,
  }) = _TeamStudySeaMapRect;

  factory TeamStudySeaMapRect.fromJson(Map<String, dynamic> json) =>
      _$TeamStudySeaMapRectFromJson(json);
}

@unfreezed
class TeamStudySeaMapPointState with _$TeamStudySeaMapPointState {
  factory TeamStudySeaMapPointState({
    int? teamId,
    int? nodeId,
    int? number,
    int? isFinish,
    int? isReceiveReward,
    int? receiveTime,
    // 节点奖励类型
    TeamStudySeaMapRewardType? nodeRewardType,
    List<TeamStudySeaMapPointReward>? rewardList,
  }) = _TeamStudySeaMapPointState;

  factory TeamStudySeaMapPointState.fromJson(Map<String, dynamic> json) =>
      _$TeamStudySeaMapPointStateFromJson(json);
}

@freezed
class TeamStudySeaMapPointReward with _$TeamStudySeaMapPointReward {
  factory TeamStudySeaMapPointReward({
    int? id,
    // 宝箱奖励类型(1道具)
    int? type,
    int? num,
    String? name,
    String? desc,
    String? img,
  }) = _TeamStudySeaMapPointReward;

  factory TeamStudySeaMapPointReward.fromJson(Map<String, dynamic> json) =>
      _$TeamStudySeaMapPointRewardFromJson(json);
}
