import 'dart:math';
import 'dart:ui';

import 'package:flutter/cupertino.dart';
import 'package:jojo_flutter_base/widgets/common/page_loading_v25.dart';

import '../../treasure_chest_page/treasure_chest_state.dart';
import 'model/team_study_sea_map_model.dart';

enum TeamStudySeaMapRewardState {
  none,
  opened, // 已开宝箱
  openable, // 可开未开宝箱
  locked, // 为解锁宝箱
  lockedNext, // 下一个未解锁宝箱
}

class TeamStudySeaMapState {
  final PageStatus pageStatus;
  final Exception? exception;
  final TeamStudySeaMapModel? mapConfig;
  final List<TeamStudySeaMapPointState>? mapNodeList;
  final List<double>? pointLayerGroupHeights; // 节点层 每组高度
  final List<int>? pointLayerGroupPointCounts; // 节点层 每组奖励数量
  final List<double>? rewardLayerGroupHeights; // 奖励层 每组高度
  final List<int>? rewardLayerGroupPointCounts; // 奖励层 每组奖励数量

  TeamStudySeaMapState(
    this.pageStatus, {
    this.exception,
    this.mapConfig,
    this.mapNodeList,
    this.rewardLayerGroupHeights,
    this.rewardLayerGroupPointCounts,
    this.pointLayerGroupHeights,
    this.pointLayerGroupPointCounts,
  });

  TeamStudySeaMapState copyWith(
    PageStatus? pageStatus, {
    Exception? exception,
    TeamStudySeaMapModel? mapConfig,
    List<TeamStudySeaMapPointState>? mapNodeList,
    List<double>? pointLayerGroupHeights,
    List<int>? pointLayerGroupPointCounts,
    List<double>? rewardLayerGroupHeights,
    List<int>? rewardLayerGroupPointCounts,
  }) {
    return TeamStudySeaMapState(
      pageStatus ?? this.pageStatus,
      exception: exception ?? this.exception,
      mapConfig: mapConfig ?? this.mapConfig,
      mapNodeList: mapNodeList ?? this.mapNodeList,
      pointLayerGroupHeights:
          pointLayerGroupHeights ?? this.pointLayerGroupHeights,
      pointLayerGroupPointCounts:
          pointLayerGroupPointCounts ?? this.pointLayerGroupPointCounts,
      rewardLayerGroupHeights:
          rewardLayerGroupHeights ?? this.rewardLayerGroupHeights,
      rewardLayerGroupPointCounts:
          rewardLayerGroupPointCounts ?? this.rewardLayerGroupPointCounts,
    );
  }

  TeamStudySeaMapPointRes? getBoatSpine() {
    for (var spine in (mapConfig?.pointSpine ?? [])) {
      if (spine.type == 'boat') {
        return spine;
      }
    }
    return null;
  }

  /// 获取当前节点（最新完成的节点）
  TeamStudySeaMapPointState getCurrentPointState() {
    var list = mapNodeList ?? [];
    var index = 0;
    for (var i = 0; i < list.length; i++) {
      if (list[i].isFinish == 1) {
        index = i;
      } else {
        break;
      }
    }
    return list[index];
  }

  TeamStudySeaMapPoint getCurrentPoint() {
    var curState = getCurrentPointState();
    var list = mapConfig?.points ?? [];
    for (var i = 0; i < list.length; i++) {
      if (list[i].id == curState.nodeId) {
        return list[i];
      }
    }
    return list[0];
  }

  /// 获取下一个奖励节点
  TeamStudySeaMapPointState? getNextRewardPointState() {
    var list = mapNodeList ?? [];
    var currentIndex = list.indexOf(getCurrentPointState());
    for (var i = currentIndex + 1; i < list.length; i++) {
      var node = list[i];
      if (node.nodeRewardType != TeamStudySeaMapRewardType.none &&
          node.isReceiveReward == 0) {
        return list[i];
      }
    }
    return null;
  }

  /// 获取节点对应的宝箱状态
  TeamStudySeaMapPointState getPointState(TeamStudySeaMapPoint point) {
    var list = mapNodeList ?? [];
    for (var i = 0; i < list.length; i++) {
      if (list[i].nodeId == point.id) {
        return list[i];
      }
    }
    return TeamStudySeaMapPointState();
  }

  /// 获取奖励状态
  TeamStudySeaMapRewardState getRewardState(
      TeamStudySeaMapPointState pointState) {
    if (pointState.nodeRewardType == TeamStudySeaMapRewardType.none) {
      return TeamStudySeaMapRewardState.none;
    }
    if (pointState.isFinish == 1 && pointState.isReceiveReward == 1) {
      return TeamStudySeaMapRewardState.opened;
    }
    if (pointState.isFinish == 1 && pointState.isReceiveReward == 0) {
      return TeamStudySeaMapRewardState.openable;
    }
    if (pointState.isFinish == 0) {
      var nextRewardPointState = getNextRewardPointState();
      if (pointState.nodeId == nextRewardPointState?.nodeId) {
        return TeamStudySeaMapRewardState.lockedNext;
      }
      return TeamStudySeaMapRewardState.locked;
    }
    return TeamStudySeaMapRewardState.none;
  }

  /// 获取奖励图片key
  String getRewardImageKey(TeamStudySeaMapPointState pointState) {
    String imageKey = '';
    switch (pointState.nodeRewardType) {
      case TeamStudySeaMapRewardType.reward:
        imageKey = pointState.isReceiveReward == 0 ? 'reward' : 'reward_open';
        break;
      case TeamStudySeaMapRewardType.rewardHigh:
        imageKey = pointState.isReceiveReward == 0
            ? 'high_reward'
            : 'high_reward_open';
        break;
      case TeamStudySeaMapRewardType.photo:
        imageKey = pointState.isReceiveReward == 0 ? 'photo' : 'photo_done';
        break;
      default:
        imageKey = 'reward';
    }
    return imageKey;
  }

  /// 是否显示对钩
  bool getRewardShowCheck(TeamStudySeaMapRewardState rewardState) {
    return rewardState == TeamStudySeaMapRewardState.opened;
  }

  /// 是否显示光圈
  bool getRewardShowLight(TeamStudySeaMapRewardState rewardState) {
    return rewardState == TeamStudySeaMapRewardState.openable;
  }

  /// 显示比例
  double getRewardScale(TeamStudySeaMapRewardState rewardState) {
    switch (rewardState) {
      case TeamStudySeaMapRewardState.opened:
      case TeamStudySeaMapRewardState.locked:
        return 0.8;
      default:
        return 1;
    }
  }

  /// 获取奖励气泡图片key
  String getRewardBubbleImageKey(TeamStudySeaMapRewardState rewardState) {
    switch (rewardState) {
      case TeamStudySeaMapRewardState.openable:
      case TeamStudySeaMapRewardState.opened:
        return 'bubble_yellow';
      default:
        return 'bubble_white';
    }
  }

  /// 获取宝箱位置
  Rect? getPointRewardRect(TeamStudySeaMapPoint point) {
    TeamStudySeaMapPointState pointState = getPointState(point);
    var rewardState = getRewardState(pointState);
    if (rewardState == TeamStudySeaMapRewardState.none) {
      return null;
    }

    bool showLight = getRewardShowLight(rewardState);

    double pointLeft = point.rect?.x ?? 0;
    double pointTop = (point.rect?.y ?? 0);
    double pointWidth = point.rect?.w ?? 65;

    double width = showLight ? 173 : 130;
    double height = showLight ? 173 : 134;
    double left = pointLeft - (width - pointWidth) / 2;
    double top = pointTop - height + (showLight ? 26 : 8);

    return Rect.fromLTWH(
      left,
      top,
      width,
      height,
    );
  }

  /// 获取宝箱位置
  Rect? getPointBoatRect({TeamStudySeaMapPoint? point}) {
    var boatPoint = point ?? getCurrentPoint();
    double pointLeft = boatPoint.rect?.x ?? 0;
    double pointTop = boatPoint.rect?.y ?? 0;
    double pointWidth = boatPoint.rect?.w ?? 65;
    double pointHeight = boatPoint.rect?.w ?? 65;

    double width = 78;
    double height = 78;
    double left = pointLeft - (width - pointWidth) / 2;
    double top = pointTop + pointHeight;

    return Rect.fromLTWH(
      left,
      top,
      width,
      height,
    );
  }

  /// 获取当前组的点
  List<TeamStudySeaMapPoint> getPointsForLayer(
      int groupIndex, List<int> groupPointCounts) {
    if (groupIndex >= groupPointCounts.length) {
      return [];
    }

    int start = 0;
    for (var i = 0; i < groupPointCounts.length; i++) {
      if (i < groupIndex) {
        start += groupPointCounts[i];
      }
    }
    int end = start + groupPointCounts[groupIndex];

    List<TeamStudySeaMapPoint> points = [];
    var allPoints = mapConfig?.points ?? [];
    for (var i = start; i < end; i++) {
      var point = allPoints[i];
      points.add(point);
    }
    return points;
  }

  /// 获取当前组开始的高度
  double getStartHeightForLayer(int index, List<double> groupHeights) {
    if (index >= groupHeights.length) {
      return 0;
    }

    double startHeight = 0;
    for (var i = 0; i < index; i++) {
      startHeight += groupHeights[i];
    }
    return startHeight;
  }

  /// 根据背景图高度 获取全部组的高度
  List<double> getLayerGroupHeights(
      double height, List<double> allGroupHeights) {
    double totalHeight = 0;
    List<double> groupHeights = [];
    for (var i = 0; i < allGroupHeights.length; i++) {
      totalHeight += allGroupHeights[i];
      if (totalHeight <= height) {
        groupHeights.add(allGroupHeights[i]);
      }
    }

    if (totalHeight < height) {
      groupHeights.add(height - totalHeight);
    }

    return groupHeights;
  }

  /// 根据地图节点数量 获取全部组的节点数量
  List<int> getLayerGroupPointCounts(
      int count, List<int> allGroupRewardCounts) {
    int totalCounts = 0;
    List<int> groupCounts = [];
    for (var i = 0; i < allGroupRewardCounts.length; i++) {
      totalCounts += allGroupRewardCounts[i];
      if (totalCounts <= count) {
        groupCounts.add(allGroupRewardCounts[i]);
      }
    }

    if (totalCounts < count) {
      groupCounts.add(count - totalCounts);
    }

    return groupCounts;
  }

  /// 节点图层 默认全部组的高度
  List<double> getAllPointLayerGroupHeights() {
    return [340, 442, 475, 214, 419, 440, 240, 398, 442];
  }

  /// 节点图层 默认全部组的节点数量
  List<int> getAllPointLayerGroupPointCounts() {
    return [0, 5, 5, 2, 5, 5, 2, 4, 2];
  }

  /// 奖励图层 默认全部组的高度
  List<double> getAllRewardLayerGroupHeights() {
    return [345, 300, 475, 214, 419, 440, 240, 398, 579];
  }

  /// 奖励图层 默认全部组的节点数量
  List<int> getAllRewardLayerGroupPointCounts() {
    return [1, 4, 5, 2, 5, 5, 2, 4, 2];
  }

  /// 获取宝箱数据
  List<TreasureChestData> getPointRewardData(TeamStudySeaMapPointState point) {
    List<TreasureChestData> data = [];
    point.rewardList?.forEach((element) {
      data.add(TreasureChestData(
        name: element.name ?? '',
        num: element.num ?? 0,
        icon: element.img ?? '',
        desc: element.desc ?? '',
      ));
    });
    return data;
  }

  TeamStudySeaMapPointState getMockPointState(TeamStudySeaMapPoint point) {
    var model = TeamStudySeaMapPointState();
    model.number = point.id;
    model.nodeId = point.id;
    model.isFinish = point.id! < 17 ? 1 : 0;
    model.isReceiveReward = point.id! < 10 ? 0 : 1;
    model.nodeRewardType = point.id! % 3 == 0
        ? TeamStudySeaMapRewardType.photo
        : (point.id! % 3 == 1
            ? TeamStudySeaMapRewardType.rewardHigh
            : TeamStudySeaMapRewardType.reward);
    // model.isFinish = 1;
    // model.isReceiveReward = 0;
    return model;
  }

  /// 计算合适的节点奖励分组高度
  List<_MapRewardGroupModel> autoCalPointLayerSeparated() {
    double minGroupHeight = 100;
    double maxGroupHeight = 300;

    var points = mapConfig?.points ?? [];
    List<_MapPointRewardRectModel> rewardRects = _getPointRewardRects();
    List<_MapRewardGroupModel> models = [];

    double lastGroupHeight = 0;
    List<Rect> groupRects = [];

    _MapRewardGroupModel model = _MapRewardGroupModel();
    model.pointRewards = [];
    model.rects = [];

    for (var i = 0; i < rewardRects.length - 1; i++) {
      var point = rewardRects[i].point;
      Rect rect1 = rewardRects[i].rect;
      Rect rect2 = rewardRects[i + 1].rect;

      model.pointRewards.add(point);
      model.rects.add(rect1);
      double currentGroupHeight = 0;

      var pointOffset = (rect1.top + rect1.height) - rect2.top;
      print('weich map point$i - point${i + 1} offset:$pointOffset');

      if (rect2.top > (rect1.top + rect1.height)) {
        currentGroupHeight = rect2.top;
      } else if ((rect1.top + rect1.height) - rect2.top < 60) {
        currentGroupHeight = rect2.top + 10;
      } else {
        groupRects.add(rect2);
        continue;
      }

      double offset = currentGroupHeight - lastGroupHeight;
      if (offset > maxGroupHeight) {
        if (groupRects.isEmpty) {
          _MapRewardGroupModel empty = _MapRewardGroupModel();
          empty.startHeight = lastGroupHeight;
          empty.endHeight = currentGroupHeight - rect2.height;
          empty.height = currentGroupHeight - rect2.height - lastGroupHeight;
          models.add(empty);

          lastGroupHeight = currentGroupHeight - rect2.height;
          offset = rect2.height;
        }
      }

      model.startHeight = lastGroupHeight;
      model.endHeight = currentGroupHeight;
      model.height = offset;
      models.add(model);
      model = _MapRewardGroupModel();

      lastGroupHeight = currentGroupHeight;
      groupRects = [];
    }

    double fullHeight = mapConfig?.bgHeight ?? 0;
    if (fullHeight > 0) {
      _MapRewardGroupModel empty = _MapRewardGroupModel();
      empty.startHeight = lastGroupHeight;
      empty.endHeight = fullHeight;
      empty.height = fullHeight - lastGroupHeight;
      models.add(empty);
    }

    return models;
  }

  List<_MapPointRewardRectModel> _getPointRewardRects() {
    List<_MapPointRewardRectModel> pointRects = [];
    var points = mapConfig?.points ?? [];
    for (var i = 0; i < points.length; i++) {
      var point = points[i];
      var rect = getPointRewardRect(point);
      if (rect != null) {
        _MapPointRewardRectModel model = _MapPointRewardRectModel();
        model.point = point;
        model.rect = rect;
        pointRects.add(model);
      }
    }
    return pointRects;
  }
}

class _MapPointRewardRectModel {
  TeamStudySeaMapPoint point = TeamStudySeaMapPoint();
  Rect rect = const Rect.fromLTWH(0, 0, 0, 0);
}

class _MapRewardGroupModel {
  double startHeight = 0;
  double endHeight = 0;
  double height = 0;
  List<TeamStudySeaMapPoint> pointRewards = [];
  List<Rect> rects = [];
}
